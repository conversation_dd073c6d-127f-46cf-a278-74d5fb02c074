"""
文本处理服务实现
从翻译服务中抽取的文本处理逻辑
"""

import re
from typing import List, Dict, Tuple
from app.interfaces.services.translation_engine_interface import ITextProcessor


class TextProcessorService(ITextProcessor):
    """文本处理服务实现"""

    def __init__(self):
        # 定义不应该翻译的特殊字符模式
        self.skip_patterns = [
            r'^[|\-\+\=\*\#\@\$\%\^\&\(\)\[\]\{\}\<\>\?\/\\]+$',  # 纯特殊字符
            r'^\d+[\.\,\-\+\*\/\=]*\d*$',  # 纯数字和数学符号
            r'^[\.\,\;\:\!\?\-\+\*\/\=\(\)\[\]]+$',  # 纯标点符号
            r'^[a-zA-Z0-9\.\-\_]+$',  # 纯英文数字组合
            # 修改：只过滤2个或以上中文字符+特殊字符的组合，允许单个中文字符
            r'^[\u4e00-\u9fff]{2,}[\|\-\+\=\*\#\@\$\%\^\&\(\)\[\]\{\}\<\>\?\/\\]+$',  # 2个或以上中文+特殊字符
            r'^[\|\-\+\=\*\#\@\$\%\^\&\(\)\[\]\{\}\<\>\?\/\\]+[\u4e00-\u9fff]{2,}$',  # 特殊字符+2个或以上中文
        ]
        self.compiled_patterns = [re.compile(pattern) for pattern in self.skip_patterns]

    def should_skip_translation(self, text: str) -> bool:
        """判断文本是否应该跳过翻译"""
        if not text or len(text.strip()) == 0:
            return True

        # 检查是否匹配跳过模式
        for pattern in self.compiled_patterns:
            if pattern.match(text.strip()):
                return True

        # 修改：允许单个中文字符进行翻译
        # 如果文本太短且主要是特殊字符，跳过
        if len(text.strip()) <= 3:
            chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
            # 修改条件：只有当没有中文字符时才跳过，单个中文字符允许翻译
            if len(chinese_chars) == 0:  # 只有当没有中文字符时才跳过
                return True

        return False

    def preprocess_text(self, text: str) -> str:
        """预处理文本"""
        if not isinstance(text, str):
            return ""

        # 去除首尾空白
        processed_text = text.strip()

        # 如果文本为空，返回空字符串
        if not processed_text:
            return ""

        return processed_text
    
    def postprocess_text(self, text: str) -> str:
        """后处理文本"""
        if not isinstance(text, str):
            return ""
        
        # 去除首尾空白和引号
        processed_text = text.strip().strip('"').strip("'")
        
        return processed_text
    
    def deduplicate_texts(self, texts: List[str]) -> Tuple[List[str], Dict[str, int]]:
        """
        文本去重并保持顺序
        
        Returns:
            tuple: (去重后的文本列表, 文本索引映射)
        """
        unique_texts = []
        text_index_map = {}
        
        for i, text in enumerate(texts):
            processed_text = self.preprocess_text(text)
            
            # 只处理非空文本
            if processed_text and processed_text not in text_index_map:
                text_index_map[processed_text] = len(unique_texts)
                unique_texts.append(processed_text)
        
        return unique_texts, text_index_map
    
    def validate_text(self, text: str) -> bool:
        """验证文本是否有效"""
        if not isinstance(text, str):
            return False
        
        processed_text = self.preprocess_text(text)
        return len(processed_text) > 0
    
    def split_large_text(self, text: str, max_length: int = 2000) -> List[str]:
        """分割大文本"""
        if len(text) <= max_length:
            return [text]
        
        # 简单的按句号分割策略
        sentences = text.split('。')
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk + sentence + '。') <= max_length:
                current_chunk += sentence + '。'
            else:
                if current_chunk:
                    chunks.append(current_chunk.rstrip('。'))
                current_chunk = sentence + '。'
        
        if current_chunk:
            chunks.append(current_chunk.rstrip('。'))
        
        return chunks
    
    def merge_translation_results(
        self, 
        original_texts: List[str], 
        unique_results: List[Dict], 
        text_index_map: Dict[str, int]
    ) -> List[Dict]:
        """合并翻译结果"""
        merged_results = []
        
        for original_text in original_texts:
            processed_text = self.preprocess_text(original_text)
            
            if processed_text in text_index_map:
                index = text_index_map[processed_text]
                if index < len(unique_results):
                    result = unique_results[index].copy()
                    result["original"] = original_text  # 保持原始文本
                    merged_results.append(result)
                else:
                    # 索引超出范围的错误处理
                    merged_results.append({
                        "success": False,
                        "error": "索引超出范围",
                        "original": original_text
                    })
            else:
                # 空文本或无效文本的处理
                merged_results.append({
                    "success": False,
                    "error": "文本为空或无效",
                    "original": original_text
                })
        
        return merged_results


# 创建全局实例
text_processor_service = TextProcessorService()
