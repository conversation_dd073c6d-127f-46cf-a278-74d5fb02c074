"""
DeepSeek翻译引擎 - 基础设施层
"""

import asyncio
import aiohttp
import json
from typing import List, Dict, Any
from app.config.config import get_settings


class DeepSeekTranslationEngine:
    """DeepSeek翻译引擎实现"""

    def __init__(self):
        """初始化DeepSeek翻译引擎"""
        settings = get_settings()

        self.api_key = settings.deepseek_api_key
        self.api_url = settings.deepseek_api_url
        self.model = settings.deepseek_model
        self.timeout = settings.deepseek_timeout
        self.max_tokens = settings.deepseek_max_tokens
        self.temperature = settings.deepseek_temperature



    async def translate_text(self, text: str, from_lang: str, to_lang: str) -> Dict[str, Any]:
        """
        翻译文本

        Args:
            text: 要翻译的文本
            from_lang: 源语言代码
            to_lang: 目标语言代码

        Returns:
            翻译结果字典
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # 构建翻译提示
            target_language_name = self._get_language_name(to_lang)

            if from_lang == "auto":
                prompt = f"请翻译以下文本到{target_language_name}：{text}"
            else:
                source_language_name = self._get_language_name(from_lang)
                prompt = f"请将以下{source_language_name}文本翻译成{target_language_name}：{text}"

            data = {
                "model": self.model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature
            }

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(self.api_url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        translated_text = result["choices"][0]["message"]["content"].strip()

                        return {
                            "success": True,
                            "translated_text": translated_text,
                            "from_lang": from_lang,
                            "to_lang": to_lang
                        }
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"API请求失败: {response.status} - {error_text}"
                        }

        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": "请求超时"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"翻译失败: {str(e)}"
            }

    def _get_language_name(self, lang_code: str) -> str:
        """获取语言名称"""
        language_names = {
            "zh": "中文",
            "en": "英语",
            "may": "马来语",
            "hkm": "高棉语",
            "id": "印尼语",
            "bur": "缅甸语",
            "fil": "菲律宾语",
            "th": "泰语",
            "vie": "越南语",
            "tam": "泰米尔语",
            "lao": "老挝语",
            "auto": "自动检测"
        }
        return language_names.get(lang_code, f"未知语言({lang_code})")


# 创建全局实例
deepseek_translation_engine = DeepSeekTranslationEngine()
