"""
百度翻译引擎实现 -
"""

import time
import asyncio
import aiohttp
from typing import List, Dict, Any
from app.interfaces.services.translation_engine_interface import ITranslationEngine
from app.models.entities.translation_engine_entity import (
    TranslationEngineConfig,
    TranslationRequest,
    TranslationResponse,
    BatchTranslationResult
)
from app.services.external.baidu_api_adapter import (
    BaiduSignatureGenerator,
    BaiduApiClient,
    BaiduApiRequestBuilder
)
from app.services.infrastructure.text_processor_service import text_processor_service
from app.config.config import get_settings



class BaiduTranslationEngine(ITranslationEngine):
    """百度翻译引擎实现"""

    def __init__(self):
        """初始化百度翻译引擎"""
        settings = get_settings()

        # 创建配置实体
        self.config = TranslationEngineConfig(
            app_id=settings.baidu_app_id,
            secret_key=settings.baidu_secret_key,
            api_url="https://fanyi-api.baidu.com/api/trans/vip/translate",
            timeout=settings.baidu_api_timeout,
            max_concurrent=settings.baidu_max_concurrent
        )

        # 初始化组件
        self.signature_generator = BaiduSignatureGenerator()
        self.api_client = BaiduApiClient(self.config.api_url)
        self.request_builder = BaiduApiRequestBuilder(self.signature_generator)
        self.text_processor = text_processor_service

        # 验证配置
        if not self.validate_config():
            raise ValueError("百度翻译配置不完整，请检查.env文件中的BAIDU_APP_ID和BAIDU_SECRET_KEY")

        self._log_initialization()

    async def translate_single(self, request: TranslationRequest) -> TranslationResponse:
        """翻译单个文本"""
        # 预处理文本
        processed_text = self.text_processor.preprocess_text(request.text)

        if not processed_text:
            return TranslationResponse(
                success=False,
                original=request.text,
                error="文本为空"
            )

        # 检查是否应该跳过翻译（避免特殊字符被翻译成表情符号）
        if hasattr(self.text_processor, 'should_skip_translation') and self.text_processor.should_skip_translation(processed_text):
            return TranslationResponse(
                success=True,
                original=request.text,
                translated=request.text,  # 返回原文，不翻译
                from_lang=request.from_lang,
                to_lang=request.to_lang
            )

        # 构建请求参数
        params = self.request_builder.build_request_params(
            processed_text,
            request.from_lang,
            request.to_lang,
            self.config.app_id,
            self.config.secret_key
        )

        # 调用API
        result = await self.api_client.call_api(params, self.config.timeout)

        # 构建响应
        if result["success"]:
            translated_text = self.text_processor.postprocess_text(result["translated"])
            return TranslationResponse(
                success=True,
                original=request.text,
                translated=translated_text,
                from_lang=request.from_lang,
                to_lang=request.to_lang
            )
        else:
            return TranslationResponse(
                success=False,
                original=request.text,
                error=result["error"]
            )

    async def translate_batch(
        self,
        texts: List[str],
        from_lang: str,
        to_lang: str,
        max_concurrent: int = None  # 如果为None，使用配置中的值
    ) -> BatchTranslationResult:
        """批量翻译"""
        pass
        start_time = time.time()

        # 如果没有指定并发数，使用配置中的值
        if max_concurrent is None:
            max_concurrent = self.config.max_concurrent

        # 文本去重
        unique_texts, text_index_map = self.text_processor.deduplicate_texts(texts)
        pass

        if not unique_texts:
            return BatchTranslationResult(
                translations=[],
                success_count=0,
                failed_count=0,
                total_count=len(texts),
                unique_count=0,
                duration=0.0
            )

        # 并发翻译
        unique_results = await self._concurrent_translate_unique_texts(
            unique_texts, from_lang, to_lang, max_concurrent
        )

        # 合并结果
        all_results = self.text_processor.merge_translation_results(
            texts, unique_results, text_index_map
        )

        # 统计结果
        success_count = sum(1 for r in unique_results if r.get("success", False))
        failed_count = len(unique_results) - success_count
        duration = round(time.time() - start_time, 2)

        # 转换为响应实体
        translation_responses = [
            TranslationResponse(
                success=result.get("success", False),
                original=result.get("original", ""),
                translated=result.get("translated"),
                from_lang=from_lang,
                to_lang=to_lang,
                error=result.get("error")
            ) for result in all_results
        ]

        self._log_batch_result(success_count, failed_count, duration, len(unique_texts))

        return BatchTranslationResult(
            translations=translation_responses,
            success_count=success_count,
            failed_count=failed_count,
            total_count=len(texts),
            unique_count=len(unique_texts),
            duration=duration
        )

    async def _concurrent_translate_unique_texts(
        self,
        unique_texts: List[str],
        from_lang: str,
        to_lang: str,
        max_concurrent: int
    ) -> List[Dict[str, Any]]:
        """并发翻译唯一文本"""
        # 创建异步会话
        connector = aiohttp.TCPConnector(limit=max_concurrent)
        timeout = aiohttp.ClientTimeout(total=self.config.timeout)

        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            # 创建并发任务
            semaphore = asyncio.Semaphore(max_concurrent)

            async def translate_with_semaphore(text):
                async with semaphore:
                    return await self._translate_single_text_with_session(
                        session, text, from_lang, to_lang
                    )

            # 执行并发翻译
            results = await asyncio.gather(
                *[translate_with_semaphore(text) for text in unique_texts],
                return_exceptions=True
            )

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": f"并发执行错误: {str(result)}",
                    "original": unique_texts[i]
                })
            else:
                processed_results.append(result)

        return processed_results

    async def _translate_single_text_with_session(
        self,
        session: aiohttp.ClientSession,
        text: str,
        from_lang: str,
        to_lang: str
    ) -> Dict[str, Any]:
        """使用会话翻译单个文本"""
        # 预处理文本
        processed_text = self.text_processor.preprocess_text(text)

        if not processed_text:
            return {
                "success": False,
                "error": "文本为空",
                "original": text
            }

        # 检查是否应该跳过翻译（避免特殊字符被翻译成表情符号）
        if hasattr(self.text_processor, 'should_skip_translation') and self.text_processor.should_skip_translation(processed_text):
            return {
                "success": True,
                "original": text,
                "translated": text,  # 返回原文，不翻译
                "from_lang": from_lang,
                "to_lang": to_lang
            }

        # 构建请求参数
        params = self.request_builder.build_request_params(
            processed_text,
            from_lang,
            to_lang,
            self.config.app_id,
            self.config.secret_key
        )

        try:
            async with session.get(self.config.api_url, params=params) as response:
                result = await response.json()

                if 'trans_result' in result:
                    translated_text = result['trans_result'][0]['dst']
                    processed_translation = self.text_processor.postprocess_text(translated_text)

                    return {
                        "success": True,
                        "original": text,
                        "translated": processed_translation,
                        "from_lang": from_lang,
                        "to_lang": to_lang
                    }
                else:
                    error_code = result.get('error_code', 'unknown')
                    error_msg = result.get('error_msg', '未知错误')
                    return {
                        "success": False,
                        "error": f"百度翻译API错误: {error_code} - {error_msg}",
                        "original": text
                    }

        except Exception as e:
            return {
                "success": False,
                "error": f"异步翻译错误: {str(e)}",
                "original": text
            }

    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            "app_id": self.config.app_id,
            "secret_key_preview": self.config.secret_key_preview,
            "api_url": self.config.api_url,
            "timeout": self.config.timeout,
            "max_concurrent": self.config.max_concurrent
        }

    def validate_config(self) -> bool:
        """验证配置"""
        try:
            return bool(self.config.app_id and self.config.secret_key)
        except Exception:
            return False

    def _log_initialization(self):
        print(f"  TIMEOUT: {self.config.timeout}秒")

    def _log_batch_result(self, success_count: int, failed_count: int, duration: float, unique_count: int):
        """记录批量翻译结果"""
        total_count = success_count + failed_count
        if total_count > 0:
            error_rate = failed_count / total_count
            # 错误率监控（安全措施）
            if error_rate > 0.2:  # 错误率超过20%时记录警告
                print(f"⚠️ 翻译错误率较高: {error_rate:.1%} ({failed_count}/{total_count})")
                print(f"建议检查网络状况或降低并发数")


# 创建全局实例
try:
    baidu_translation_engine = BaiduTranslationEngine()
except ValueError as e:
    baidu_translation_engine = None