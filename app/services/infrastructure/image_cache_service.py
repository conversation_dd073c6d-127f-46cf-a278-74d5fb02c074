"""
图片OCR缓存服务
"""

import hashlib
import json
import time
from pathlib import Path
from typing import Optional, Dict, Any
from app.config.config import get_settings


class ImageCacheService:
    """图片OCR缓存服务"""
    
    def __init__(self):
        self.settings = get_settings()
        self.cache_dir = Path("cache/img")  # 改为img文件夹
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        # 默认缓存TTL：-1=永久缓存，和翻译缓存清理间隔保持一致
        self.default_ttl_minutes = -1
    
    def generate_cache_key(self, base64_image: str, target_language: str) -> str:
        """生成缓存键"""
        # 移除可能的data:image前缀
        clean_base64 = base64_image
        if base64_image.startswith('data:image'):
            clean_base64 = base64_image.split(',')[1] if ',' in base64_image else base64_image
        
        # 使用图片内容和目标语言生成哈希
        content = f"{clean_base64}_{target_language}"
        hash_obj = hashlib.sha256(content.encode('utf-8'))
        return f"img_{hash_obj.hexdigest()[:16]}"
    
    def get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.json"
    
    def is_cache_valid(self, cache_file: Path, ttl_minutes: int) -> bool:
        """检查缓存是否有效"""
        if not cache_file.exists():
            return False
        
        # -1 表示永久缓存
        if ttl_minutes == -1:
            return True
        
        # 0 表示不缓存
        if ttl_minutes == 0:
            return False
        
        # 检查缓存时间
        file_mtime = cache_file.stat().st_mtime
        current_time = time.time()
        cache_age_minutes = (current_time - file_mtime) / 60
        
        return cache_age_minutes < ttl_minutes
    
    def get_cached_result(self, base64_image: str, target_language: str) -> Optional[Dict[str, Any]]:
        """获取缓存的OCR结果"""
        try:
            cache_key = self.generate_cache_key(base64_image, target_language)
            cache_file = self.get_cache_file_path(cache_key)

            if not cache_file.exists():
                return None

            with open(cache_file, 'r', encoding='utf-8') as f:
                cached_data = json.load(f)

            # 添加缓存信息
            cached_data['from_cache'] = True
            cached_data['cache_key'] = cache_key

            return cached_data

        except Exception:
            return None
    
    def save_cache_result(self, base64_image: str, target_language: str, result_data: Dict[str, Any]) -> str:
        """保存OCR结果到缓存"""
        try:
            cache_key = self.generate_cache_key(base64_image, target_language)
            cache_file = self.get_cache_file_path(cache_key)

            # 准备缓存数据（移除一些不需要缓存的字段）
            cache_data = result_data.copy()
            cache_data.pop('from_cache', None)
            cache_data.pop('cache_key', None)
            cache_data.pop('processing_time', None)  # 处理时间不缓存

            # 添加缓存元数据
            cache_data['cached_at'] = time.time()
            cache_data['target_language'] = target_language

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

            return cache_key

        except Exception:
            return ""
    



# 创建全局实例
image_cache_service = ImageCacheService()
