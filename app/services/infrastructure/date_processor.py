"""
日期预处理服务
在翻译前直接将中文日期替换为目标语言的标准格式，避免翻译API的错误处理
"""

import json
import re
from typing import Dict, List, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass


@dataclass
class DateMatch:
    """日期匹配结果"""
    date_text: str      # 原始中文日期
    start_pos: int      # 开始位置
    end_pos: int        # 结束位置
    replacement: str    # 替换后的目标语言日期
    date_type: str      # 日期类型（basic/with_year）


@dataclass
class DateProcessResult:
    """日期处理结果"""
    original_text: str              # 原始文本
    processed_text: str             # 处理后文本
    has_dates: bool                 # 是否包含日期
    matches: List[DateMatch]        # 匹配的日期列表
    target_language: str            # 目标语言


class DateProcessor:
    """日期处理器"""
    
    def __init__(self):
        self.dates_dict = {}
        self.supported_languages = []
        self.load_dates_dict()
    
    def load_dates_dict(self):
        """加载日期词典"""
        dates_file = Path("data/dates.json")
        
        if not dates_file.exists():
            return

        try:
            with open(dates_file, 'r', encoding='utf-8') as f:
                self.dates_dict = json.load(f)

            # 获取支持的语言列表
            if self.dates_dict:
                first_date = next(iter(self.dates_dict.values()))
                self.supported_languages = list(first_date.keys())

        except Exception as e:
            self.dates_dict = {}
    
    def process_text(self, text: str, target_language: str) -> DateProcessResult:
        """
        处理文本中的日期
        
        Args:
            text: 原始中文文本
            target_language: 目标语言代码 (如 'en', 'th', 'vie' 等)
            
        Returns:
            DateProcessResult: 处理结果
        """
        if not text or not isinstance(text, str):
            return DateProcessResult(
                original_text=text or "",
                processed_text=text or "",
                has_dates=False,
                matches=[],
                target_language=target_language
            )
        
        if target_language not in self.supported_languages:
            return DateProcessResult(
                original_text=text,
                processed_text=text,
                has_dates=False,
                matches=[],
                target_language=target_language
            )
        
        # 查找所有日期匹配
        matches = self._find_dates(text, target_language)
        
        # 执行替换
        processed_text = self._replace_dates(text, matches)
        
        return DateProcessResult(
            original_text=text,
            processed_text=processed_text,
            has_dates=len(matches) > 0,
            matches=matches,
            target_language=target_language
        )
    
    def _find_dates(self, text: str, target_language: str) -> List[DateMatch]:
        """查找文本中的所有日期"""
        matches = []
        
        # 按日期长度降序排序，优先匹配长日期（最长匹配原则）
        # 这样 "2024年6月27日" 会优先于 "6月27日" 被匹配
        sorted_dates = sorted(self.dates_dict.keys(), key=len, reverse=True)
        
        for date_text in sorted_dates:
            # 查找当前日期的所有出现位置
            start_pos = 0
            while True:
                pos = text.find(date_text, start_pos)
                if pos == -1:
                    break
                
                end_pos = pos + len(date_text)
                
                # 检查是否与已有匹配重叠
                if not self._is_overlapping(pos, end_pos, matches):
                    target_translation = self.dates_dict[date_text].get(target_language, date_text)
                    
                    # 判断日期类型
                    date_type = "with_year" if "年" in date_text else "basic"
                    
                    match = DateMatch(
                        date_text=date_text,
                        start_pos=pos,
                        end_pos=end_pos,
                        replacement=target_translation,
                        date_type=date_type
                    )
                    matches.append(match)
                
                start_pos = pos + 1
        
        # 按位置排序
        matches.sort(key=lambda x: x.start_pos)
        return matches
    
    def _is_overlapping(self, start: int, end: int, existing_matches: List[DateMatch]) -> bool:
        """检查新匹配是否与现有匹配重叠"""
        for match in existing_matches:
            if not (end <= match.start_pos or start >= match.end_pos):
                return True
        return False
    
    def _replace_dates(self, text: str, matches: List[DateMatch]) -> str:
        """执行日期替换"""
        if not matches:
            return text
        
        # 从后往前替换，避免位置偏移
        processed_text = text
        for match in reversed(matches):
            processed_text = (
                processed_text[:match.start_pos] + 
                match.replacement + 
                processed_text[match.end_pos:]
            )
        
        return processed_text
    
    def get_statistics(self) -> Dict[str, any]:
        """获取日期库统计信息"""
        basic_dates = sum(1 for date in self.dates_dict.keys() if "年" not in date)
        with_year_dates = sum(1 for date in self.dates_dict.keys() if "年" in date)
        
        return {
            "total_dates": len(self.dates_dict),
            "basic_dates": basic_dates,
            "with_year_dates": with_year_dates,
            "supported_languages": self.supported_languages,
            "language_count": len(self.supported_languages),
            "sample_dates": list(self.dates_dict.keys())[:5] if self.dates_dict else []
        }
    
    def check_date(self, date_text: str) -> bool:
        """检查单个文本是否为日期"""
        return date_text in self.dates_dict
    
    def get_translation(self, chinese_date: str, target_language: str) -> Optional[str]:
        """获取指定日期的目标语言翻译"""
        if chinese_date in self.dates_dict:
            return self.dates_dict[chinese_date].get(target_language)
        return None
    
    def reload_dates_dict(self):
        """重新加载日期词典"""
        self.load_dates_dict()


# 创建全局实例
date_processor = DateProcessor()


# 便捷函数
def process_dates(text: str, target_language: str) -> DateProcessResult:
    """
    便捷函数：处理文本中的日期
    
    Args:
        text: 中文文本
        target_language: 目标语言代码
        
    Returns:
        DateProcessResult: 处理结果
    """
    return date_processor.process_text(text, target_language)


def has_dates(text: str) -> bool:
    """
    便捷函数：检查文本是否包含日期
    
    Args:
        text: 中文文本
        
    Returns:
        bool: 是否包含日期
    """
    for date_text in date_processor.dates_dict.keys():
        if date_text in text:
            return True
    return False


if __name__ == "__main__":
    # 测试代码
    processor = DateProcessor()
    
    # 测试文本
    test_texts = [
        "今天是6月27日，天气很好。",
        "2024年6月27日是个重要的日子。",
        "六月27号开会，请准时参加。",
        "会议定在6月二十七日举行。"
    ]
    
    # 测试不同语言
    for text in test_texts:
        print(f"\n原文: {text}")
        for lang in ["en", "th", "vie"]:
            result = processor.process_text(text, lang)
            if result.has_dates:
                print(f"{lang}: {result.processed_text}")
                for match in result.matches:
                    print(f"  - {match.date_text} → {match.replacement}")
            else:
                print(f"{lang}: 无日期匹配")
