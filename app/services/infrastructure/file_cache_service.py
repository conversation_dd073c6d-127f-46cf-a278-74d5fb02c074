"""
文件缓存服务实现 - 重构后的版本
按照五层架构重构的文件缓存服务
"""

import os
import json
import asyncio
from pathlib import Path
from typing import Optional, Dict, Any, List
from app.interfaces.services.cache_service_interface import ICacheService, ICacheStorage
from app.models.entities.cache_entity import (
    CacheKey,
    CacheEntry,
    FileCacheConfig,
    CacheStatistics,
    CacheOperationResult
)
from app.models.enums.translation_enums import CacheStrategy
from app.services.infrastructure.cache_utils_service import (
    file_cache_key_generator,
    cache_serializer,
    cache_compressor,
    cache_metadata_builder
)
from app.config.config import get_settings


class FileStorage(ICacheStorage):
    """文件存储实现"""

    def __init__(self, config: FileCacheConfig):
        self.config = config
        self.cache_dir = Path(config.cache_dir)
        self.index_file = Path(config.index_file)

        # 确保缓存目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)

    async def get(self, key: str) -> Optional[bytes]:
        """获取缓存数据"""
        try:
            cache_file = self.cache_dir / f"{key}.cache"
            if cache_file.exists():
                return cache_file.read_bytes()
            return None
        except Exception as e:
            return None

    async def set(self, key: str, data: bytes, ttl_seconds: Optional[int] = None) -> bool:
        """设置缓存数据"""
        try:
            cache_file = self.cache_dir / f"{key}.cache"
            cache_file.write_bytes(data)
            return True
        except Exception as e:
            return False

    async def delete(self, key: str) -> bool:
        """删除缓存数据"""
        try:
            cache_file = self.cache_dir / f"{key}.cache"
            if cache_file.exists():
                cache_file.unlink()
                return True
            return False
        except Exception as e:
            return False

    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            cache_file = self.cache_dir / f"{key}.cache"
            return cache_file.exists()
        except Exception:
            return False

    async def clear_all(self) -> bool:
        """清空所有缓存"""
        try:
            count = 0
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
                count += 1

            print(f"🗑️ 已清空 {count} 个文件缓存")
            return True
        except Exception as e:
            return False

    async def get_file_stats(self) -> Dict[str, Any]:
        """获取文件统计信息"""
        try:
            cache_files = list(self.cache_dir.glob("*.cache"))
            total_size = sum(f.stat().st_size for f in cache_files)

            return {
                "total_files": len(cache_files),
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "cache_dir": str(self.cache_dir)
            }
        except Exception as e:
            return {"error": str(e)}


class FileCacheServiceV2(ICacheService):
    """文件缓存服务实现 - 重构版本"""

    def __init__(self):
        """初始化文件缓存服务"""
        settings = get_settings()

        # 创建配置实体
        self.config = FileCacheConfig(
            strategy=CacheStrategy.FILE_CACHE,
            ttl_seconds=settings.cache_ttl,
            cache_dir="cache/translations",
            index_file="cache/index/cache_index.json",
            compression_enabled=True,
            compression_min_size=1024,
            cleanup_interval_hours=24
        )

        # 初始化组件
        self.storage = FileStorage(self.config)
        self.key_generator = file_cache_key_generator
        self.serializer = cache_serializer
        self.compressor = cache_compressor
        self.metadata_builder = cache_metadata_builder

        pass

    async def initialize(self) -> bool:
        """初始化缓存服务"""
        try:
            # 确保缓存目录存在
            Path(self.config.cache_dir).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            return False

    async def get_cache(self, cache_key: CacheKey) -> Optional[Dict[str, Any]]:
        """获取缓存"""
        # 验证缓存键
        if not self.key_generator.validate_key(cache_key):
            return None

        # 生成文件键
        file_key = self.key_generator.generate_key(cache_key)

        # 从存储获取数据
        raw_data = await self.storage.get(file_key)
        if raw_data is None:
            return None

        try:
            # 解压数据
            decompressed_data = self.compressor.decompress(raw_data)

            # 反序列化
            cache_entry = self.serializer.deserialize(decompressed_data)

            # 检查是否过期
            if not cache_entry.is_valid:
                await self.storage.delete(file_key)
                return None

            return cache_entry.content

        except Exception as e:
            return None

    async def set_cache(self, cache_key: CacheKey, data: Dict[str, Any]) -> CacheOperationResult:
        """设置缓存"""
        # 验证缓存键
        if not self.key_generator.validate_key(cache_key):
            return CacheOperationResult.error_result("无效的缓存键")

        # 生成文件键
        file_key = self.key_generator.generate_key(cache_key)

        try:
            # 构建缓存条目
            metadata = self.metadata_builder.build_metadata(
                cache_key, file_key, self.config, "file_v2"
            )
            cache_entry = CacheEntry(metadata=metadata, content=data)

            # 序列化
            serialized_data = self.serializer.serialize(cache_entry)

            # 压缩（如果需要）
            if self.config.compression_enabled and self.compressor.should_compress(serialized_data):
                final_data = self.compressor.compress(serialized_data)
            else:
                final_data = serialized_data

            # 保存到存储
            success = await self.storage.set(file_key, final_data)

            if success:
                return CacheOperationResult.success_result("缓存保存成功", {"key": file_key})
            else:
                return CacheOperationResult.error_result("缓存保存失败")

        except Exception as e:
            return CacheOperationResult.error_result("缓存保存异常", str(e))

    async def delete_cache(self, cache_key: CacheKey) -> CacheOperationResult:
        """删除缓存"""
        # 生成文件键
        file_key = self.key_generator.generate_key(cache_key)

        try:
            success = await self.storage.delete(file_key)
            if success:
                print(f"🗑️ 文件缓存已删除: {file_key}")
                return CacheOperationResult.success_result("缓存删除成功")
            else:
                return CacheOperationResult.error_result("缓存不存在或删除失败")
        except Exception as e:
            return CacheOperationResult.error_result("缓存删除异常", str(e))

    async def exists_cache(self, cache_key: CacheKey) -> bool:
        """检查缓存是否存在"""
        file_key = self.key_generator.generate_key(cache_key)
        return await self.storage.exists(file_key)

    async def get_statistics(self) -> CacheStatistics:
        """获取缓存统计"""
        try:
            stats = await self.storage.get_file_stats()

            return CacheStatistics(
                total_entries=stats.get("total_files", 0),
                total_size_mb=stats.get("total_size_mb", 0.0)
            )
        except Exception:
            return CacheStatistics(total_entries=0, total_size_mb=0.0)

    async def clear_all_cache(self) -> CacheOperationResult:
        """清空所有缓存"""
        try:
            success = await self.storage.clear_all()
            if success:
                return CacheOperationResult.success_result("所有缓存已清空")
            else:
                return CacheOperationResult.error_result("清空缓存失败")
        except Exception as e:
            return CacheOperationResult.error_result("清空缓存异常", str(e))

    async def cleanup_expired(self) -> int:
        """清理过期缓存"""
        try:
            expired_count = 0
            cache_dir = Path(self.config.cache_dir)

            for cache_file in cache_dir.glob("*.cache"):
                try:
                    raw_data = cache_file.read_bytes()
                    decompressed_data = self.compressor.decompress(raw_data)
                    cache_entry = self.serializer.deserialize(decompressed_data)

                    if not cache_entry.is_valid:
                        cache_file.unlink()
                        expired_count += 1

                except Exception:
                    # 如果文件损坏，也删除
                    cache_file.unlink()
                    expired_count += 1

            if expired_count > 0:
                print(f"🧹 已清理 {expired_count} 个过期文件缓存")

            return expired_count
        except Exception as e:
            return 0


# 创建全局实例
file_cache_service_v2 = FileCacheServiceV2()
