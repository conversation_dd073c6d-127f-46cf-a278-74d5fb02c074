"""
城市名预处理服务
在翻译前直接将中文城市名替换为目标语言，确保地名翻译的准确性
"""

import json
import re
from typing import Dict, List, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass


@dataclass
class CityMatch:
    """城市匹配结果"""
    city_name: str      # 原始中文城市名
    start_pos: int      # 开始位置
    end_pos: int        # 结束位置
    replacement: str    # 替换后的目标语言城市名
    city_type: str      # 城市类型（city/district/county）


@dataclass
class CityProcessResult:
    """城市处理结果"""
    original_text: str              # 原始文本
    processed_text: str             # 处理后文本
    has_cities: bool                # 是否包含城市名
    matches: List[CityMatch]        # 匹配的城市列表
    target_language: str            # 目标语言


class CityProcessor:
    """城市处理器"""
    
    def __init__(self):
        self.cities_dict = {}
        self.guangxi_cities_dict = {}
        self.supported_languages = []
        self.is_pinyin_format = False  # 标识是否为拼音格式
        self.load_cities_dict()
    
    def load_cities_dict(self):
        """加载城市词典（支持新旧两种格式）"""
        # 加载主要城市词典
        cities_file = Path("data/cities.json")
        if cities_file.exists():
            try:
                with open(cities_file, 'r', encoding='utf-8') as f:
                    self.cities_dict = json.load(f)
            except Exception as e:
                self.cities_dict = {}

        # 尝试加载广西城市词典（可能已合并）
        guangxi_file = Path("data/guangxi_cities.json")
        if guangxi_file.exists():
            try:
                with open(guangxi_file, 'r', encoding='utf-8') as f:
                    self.guangxi_cities_dict = json.load(f)
            except Exception as e:
                self.guangxi_cities_dict = {}
        else:
            # 如果广西文件不存在，说明已经合并到主文件中
            self.guangxi_cities_dict = {}

        # 合并两个词典
        combined_dict = {**self.cities_dict, **self.guangxi_cities_dict}
        self.all_cities_dict = combined_dict

        # 检测词典格式并设置支持的语言列表
        if self.all_cities_dict:
            first_city = next(iter(self.all_cities_dict.values()))

            if isinstance(first_city, dict):
                # 旧格式：多语言翻译
                self.supported_languages = list(first_city.keys())
                self.is_pinyin_format = False
            elif isinstance(first_city, str):
                # 新格式：统一拼音
                self.supported_languages = ['all']  # 支持所有语言
                self.is_pinyin_format = True
            else:
                self.supported_languages = []
                self.is_pinyin_format = False
    
    def process_text(self, text: str, target_language: str) -> CityProcessResult:
        """
        处理文本中的城市名
        
        Args:
            text: 原始中文文本
            target_language: 目标语言代码 (如 'en', 'th', 'vie' 等)
            
        Returns:
            CityProcessResult: 处理结果
        """
        if not text or not isinstance(text, str):
            return CityProcessResult(
                original_text=text or "",
                processed_text=text or "",
                has_cities=False,
                matches=[],
                target_language=target_language
            )
        
        # 拼音格式支持所有语言，旧格式需要检查语言支持
        if not self.is_pinyin_format and target_language not in self.supported_languages:
            return CityProcessResult(
                original_text=text,
                processed_text=text,
                has_cities=False,
                matches=[],
                target_language=target_language
            )
        
        # 查找所有城市匹配
        matches = self._find_cities(text, target_language)
        
        # 执行替换
        processed_text = self._replace_cities(text, matches)
        
        return CityProcessResult(
            original_text=text,
            processed_text=processed_text,
            has_cities=len(matches) > 0,
            matches=matches,
            target_language=target_language
        )
    
    def _find_cities(self, text: str, target_language: str) -> List[CityMatch]:
        """查找文本中的所有城市名"""
        matches = []
        
        # 按城市名长度降序排序，优先匹配长城市名（最长匹配原则）
        # 这样 "南宁市青秀区" 会优先于 "南宁" 被匹配
        sorted_cities = sorted(self.all_cities_dict.keys(), key=len, reverse=True)
        
        for city_name in sorted_cities:
            # 查找当前城市名的所有出现位置
            start_pos = 0
            while True:
                pos = text.find(city_name, start_pos)
                if pos == -1:
                    break
                
                end_pos = pos + len(city_name)
                
                # 检查是否与已有匹配重叠
                if not self._is_overlapping(pos, end_pos, matches):
                    # 获取替换文本（拼音格式或多语言格式）
                    if self.is_pinyin_format:
                        # 新格式：使用拼音，并在后面添加空格
                        base_translation = self.all_cities_dict[city_name]
                        target_translation = self._add_space_after_pinyin(base_translation, text, end_pos)
                    else:
                        # 旧格式：获取目标语言的翻译
                        target_translation = self.all_cities_dict[city_name].get(target_language, city_name)
                    
                    # 判断城市类型
                    city_type = self._determine_city_type(city_name)
                    
                    match = CityMatch(
                        city_name=city_name,
                        start_pos=pos,
                        end_pos=end_pos,
                        replacement=target_translation,
                        city_type=city_type
                    )
                    matches.append(match)
                
                start_pos = pos + 1
        
        # 按位置排序
        matches.sort(key=lambda x: x.start_pos)
        return matches
    
    def _determine_city_type(self, city_name: str) -> str:
        """判断城市类型"""
        if "市" in city_name:
            return "city"
        elif "区" in city_name:
            return "district"
        elif "县" in city_name:
            return "county"
        else:
            return "city"  # 默认为城市
    
    def _is_overlapping(self, start: int, end: int, existing_matches: List[CityMatch]) -> bool:
        """检查新匹配是否与现有匹配重叠"""
        for match in existing_matches:
            if not (end <= match.start_pos or start >= match.end_pos):
                return True
        return False
    
    def _replace_cities(self, text: str, matches: List[CityMatch]) -> str:
        """执行城市名替换"""
        if not matches:
            return text
        
        # 从后往前替换，避免位置偏移
        processed_text = text
        for match in reversed(matches):
            processed_text = (
                processed_text[:match.start_pos] + 
                match.replacement + 
                processed_text[match.end_pos:]
            )
        
        return processed_text

    def _add_space_after_pinyin(self, pinyin: str, text: str, end_pos: int) -> str:
        """
        在城市拼音后面智能添加空格

        Args:
            pinyin: 拼音文本
            text: 原始文本
            end_pos: 城市名在原文中的结束位置

        Returns:
            str: 处理后的拼音（包含空格）
        """
        # 如果拼音已经以空格结尾，不重复添加
        if pinyin.endswith(' '):
            return pinyin

        # 检查城市名后面的字符
        if end_pos < len(text):
            next_char = text[end_pos]

            # 如果后面已经是空格或制表符，不添加空格
            if next_char in ' \t\n':
                return pinyin

            # 其他所有情况都添加空格：
            # - 中文字符：北京市 → Beijing 市
            # - 标点符号：北京， → Beijing ，
            # - 英文字母：北京City → Beijing City
            # - 数字：北京2024 → Beijing 2024
            # - 特殊符号：北京@地区 → Beijing @地区
            return pinyin + ' '

        # 如果在文本末尾，不添加空格
        return pinyin

    def get_statistics(self) -> Dict[str, any]:
        """获取城市库统计信息"""
        main_cities = len(self.cities_dict)
        guangxi_cities = len(self.guangxi_cities_dict)
        
        city_count = sum(1 for city in self.all_cities_dict.keys() if "市" in city)
        district_count = sum(1 for city in self.all_cities_dict.keys() if "区" in city)
        county_count = sum(1 for city in self.all_cities_dict.keys() if "县" in city)
        
        return {
            "total_cities": len(self.all_cities_dict),
            "main_cities": main_cities,
            "guangxi_cities": guangxi_cities,
            "city_count": city_count,
            "district_count": district_count,
            "county_count": county_count,
            "supported_languages": self.supported_languages,
            "language_count": len(self.supported_languages),
            "sample_cities": list(self.all_cities_dict.keys())[:10] if self.all_cities_dict else []
        }
    
    def check_city(self, city_name: str) -> bool:
        """检查单个文本是否为城市名"""
        return city_name in self.all_cities_dict
    
    def get_translation(self, chinese_city: str, target_language: str) -> Optional[str]:
        """获取指定城市的目标语言翻译"""
        if chinese_city in self.all_cities_dict:
            return self.all_cities_dict[chinese_city].get(target_language)
        return None
    
    def reload_cities_dict(self):
        """重新加载城市词典"""
        self.load_cities_dict()


# 创建全局实例
city_processor = CityProcessor()


# 便捷函数
def process_cities(text: str, target_language: str) -> CityProcessResult:
    """
    便捷函数：处理文本中的城市名
    
    Args:
        text: 中文文本
        target_language: 目标语言代码
        
    Returns:
        CityProcessResult: 处理结果
    """
    return city_processor.process_text(text, target_language)


def has_cities(text: str) -> bool:
    """
    便捷函数：检查文本是否包含城市名
    
    Args:
        text: 中文文本
        
    Returns:
        bool: 是否包含城市名
    """
    for city_name in city_processor.all_cities_dict.keys():
        if city_name in text:
            return True
    return False


if __name__ == "__main__":
    # 测试代码
    processor = CityProcessor()
    
    # 测试文本
    test_texts = [
        "我从北京到上海出差。",
        "南宁市青秀区是广西的政治中心。",
        "桂林山水甲天下，柳州螺蛳粉很有名。",
        "防城港市东兴市是边境城市。"
    ]
    
    # 测试不同语言
    for text in test_texts:
        print(f"\n原文: {text}")
        for lang in ["en", "th", "vie"]:
            result = processor.process_text(text, lang)
            if result.has_cities:
                print(f"{lang}: {result.processed_text}")
                for match in result.matches:
                    print(f"  - {match.city_name} → {match.replacement} ({match.city_type})")
            else:
                print(f"{lang}: 无城市匹配")
