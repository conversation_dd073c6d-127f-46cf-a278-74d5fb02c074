"""
敏感词预处理服务
在翻译前直接将中文敏感词替换为目标语言，避免百度翻译API报错
"""

import json
import re
from typing import Dict, List, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass


@dataclass
class SensitiveWordMatch:
    """敏感词匹配结果"""
    word: str           # 原始中文敏感词
    start_pos: int      # 开始位置
    end_pos: int        # 结束位置
    replacement: str    # 替换后的目标语言词


@dataclass
class ProcessResult:
    """处理结果"""
    original_text: str              # 原始文本
    processed_text: str             # 处理后文本
    has_sensitive_words: bool       # 是否包含敏感词
    matches: List[SensitiveWordMatch]  # 匹配的敏感词列表
    target_language: str            # 目标语言


class SensitiveWordProcessor:
    """敏感词处理器"""
    
    def __init__(self):
        self.words_dict = {}
        self.supported_languages = []
        self.is_pinyin_format = False  # 标识是否为拼音格式
        self.load_words_dict()
    
    def load_words_dict(self):
        """加载敏感词词典（支持新旧两种格式）"""
        words_file = Path("data/words.json")

        if not words_file.exists():
            return

        try:
            with open(words_file, 'r', encoding='utf-8') as f:
                self.words_dict = json.load(f)

            # 检测词典格式并设置支持的语言列表
            if self.words_dict:
                first_word = next(iter(self.words_dict.values()))

                if isinstance(first_word, dict):
                    # 旧格式：多语言翻译
                    self.supported_languages = list(first_word.keys())
                    self.is_pinyin_format = False
                elif isinstance(first_word, str):
                    # 新格式：统一拼音
                    self.supported_languages = ['all']  # 支持所有语言
                    self.is_pinyin_format = True
                else:
                    self.supported_languages = []
                    self.is_pinyin_format = False

        except Exception as e:
            self.words_dict = {}
            self.is_pinyin_format = False
    
    def process_text(self, text: str, target_language: str) -> ProcessResult:
        """
        处理文本中的敏感词
        
        Args:
            text: 原始中文文本
            target_language: 目标语言代码 (如 'en', 'th', 'vie' 等)
            
        Returns:
            ProcessResult: 处理结果
        """
        if not text or not isinstance(text, str):
            return ProcessResult(
                original_text=text or "",
                processed_text=text or "",
                has_sensitive_words=False,
                matches=[],
                target_language=target_language
            )
        
        # 拼音格式支持所有语言，旧格式需要检查语言支持
        if not self.is_pinyin_format and target_language not in self.supported_languages:
            return ProcessResult(
                original_text=text,
                processed_text=text,
                has_sensitive_words=False,
                matches=[],
                target_language=target_language
            )
        
        # 查找所有敏感词匹配
        matches = self._find_sensitive_words(text, target_language)
        
        # 执行替换
        processed_text = self._replace_sensitive_words(text, matches)
        
        return ProcessResult(
            original_text=text,
            processed_text=processed_text,
            has_sensitive_words=len(matches) > 0,
            matches=matches,
            target_language=target_language
        )
    
    def _find_sensitive_words(self, text: str, target_language: str) -> List[SensitiveWordMatch]:
        """查找文本中的所有敏感词"""
        matches = []
        
        # 按敏感词长度降序排序，长度相同时按字典序排序（确保稳定性）
        # 这样可以保证相同输入总是产生相同输出，避免缓存错乱
        sorted_words = sorted(self.words_dict.keys(), key=lambda x: (-len(x), x))
        
        for sensitive_word in sorted_words:
            # 查找当前敏感词的所有出现位置
            start_pos = 0
            while True:
                pos = text.find(sensitive_word, start_pos)
                if pos == -1:
                    break
                
                end_pos = pos + len(sensitive_word)
                
                # 检查是否与已有匹配重叠
                if not self._is_overlapping(pos, end_pos, matches):
                    # 获取替换文本（拼音格式或多语言格式）
                    if self.is_pinyin_format:
                        # 新格式：使用拼音，并在后面添加空格
                        base_translation = self.words_dict[sensitive_word]
                        target_translation = self._add_space_after_pinyin(base_translation, text, end_pos)
                    else:
                        # 旧格式：获取目标语言的翻译
                        target_translation = self.words_dict[sensitive_word].get(target_language, sensitive_word)
                    
                    match = SensitiveWordMatch(
                        word=sensitive_word,
                        start_pos=pos,
                        end_pos=end_pos,
                        replacement=target_translation
                    )
                    matches.append(match)
                
                start_pos = pos + 1
        
        # 按位置排序
        matches.sort(key=lambda x: x.start_pos)
        return matches
    
    def _is_overlapping(self, start: int, end: int, existing_matches: List[SensitiveWordMatch]) -> bool:
        """检查新匹配是否与现有匹配重叠"""
        for match in existing_matches:
            if not (end <= match.start_pos or start >= match.end_pos):
                return True
        return False
    
    def _replace_sensitive_words(self, text: str, matches: List[SensitiveWordMatch]) -> str:
        """执行敏感词替换"""
        if not matches:
            return text
        
        # 从后往前替换，避免位置偏移
        processed_text = text
        for match in reversed(matches):
            processed_text = (
                processed_text[:match.start_pos] + 
                match.replacement + 
                processed_text[match.end_pos:]
            )
        
        return processed_text

    def _add_space_after_pinyin(self, pinyin: str, text: str, end_pos: int) -> str:
        """
        在拼音后面智能添加空格

        Args:
            pinyin: 拼音文本
            text: 原始文本
            end_pos: 敏感词在原文中的结束位置

        Returns:
            str: 处理后的拼音（包含空格）
        """
        # 如果拼音已经以空格结尾，不重复添加
        if pinyin.endswith(' '):
            return pinyin

        # 检查敏感词后面的字符
        if end_pos < len(text):
            next_char = text[end_pos]

            # 如果后面已经是空格或制表符，不添加空格
            if next_char in ' \t\n':
                return pinyin

            # 其他所有情况都添加空格：
            # - 中文字符：习近平主席 → Xi Jinping 主席
            # - 标点符号：习近平， → Xi Jinping ，
            # - 英文字母：习近平President → Xi Jinping President
            # - 数字：习近平2024 → Xi Jinping 2024
            # - 特殊符号：习近平@主席 → Xi Jinping @主席
            return pinyin + ' '

        # 如果在文本末尾，不添加空格
        return pinyin

    def get_statistics(self) -> Dict[str, any]:
        """获取敏感词库统计信息"""
        return {
            "total_words": len(self.words_dict),
            "supported_languages": self.supported_languages,
            "language_count": len(self.supported_languages),
            "sample_words": list(self.words_dict.keys())[:5] if self.words_dict else []
        }
    
    def check_word(self, word: str) -> bool:
        """检查单个词是否为敏感词"""
        return word in self.words_dict
    
    def get_translation(self, chinese_word: str, target_language: str) -> Optional[str]:
        """获取指定敏感词的目标语言翻译"""
        if chinese_word in self.words_dict:
            if self.is_pinyin_format:
                # 新格式：直接返回拼音
                return self.words_dict[chinese_word]
            else:
                # 旧格式：获取目标语言翻译
                return self.words_dict[chinese_word].get(target_language)
        return None
    
    def reload_words_dict(self):
        """重新加载敏感词词典"""
        self.load_words_dict()


# 创建全局实例
sensitive_word_processor = SensitiveWordProcessor()


# 便捷函数
def process_sensitive_words(text: str, target_language: str) -> ProcessResult:
    """
    便捷函数：处理文本中的敏感词
    
    Args:
        text: 中文文本
        target_language: 目标语言代码
        
    Returns:
        ProcessResult: 处理结果
    """
    return sensitive_word_processor.process_text(text, target_language)


def has_sensitive_words(text: str) -> bool:
    """
    便捷函数：检查文本是否包含敏感词
    
    Args:
        text: 中文文本
        
    Returns:
        bool: 是否包含敏感词
    """
    for word in sensitive_word_processor.words_dict.keys():
        if word in text:
            return True
    return False


if __name__ == "__main__":
    # 测试代码
    processor = SensitiveWordProcessor()
    
    # 测试文本
    test_text = "习近平主席和李强总理在北京会面，讨论了重要议题。"
    
    # 测试不同语言
    for lang in ["en", "th", "vie"]:
        result = processor.process_text(test_text, lang)
