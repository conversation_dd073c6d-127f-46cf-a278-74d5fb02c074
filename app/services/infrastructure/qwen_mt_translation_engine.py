"""\nQwen3-MT翻译引擎 - 基础设施层\n"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any, List
from app.config.config import get_settings
from app.models.entities.translation_engine_entity import TranslationRequest, TranslationResponse, BatchTranslationResult


class QwenMTTranslationEngine:
    """Qwen3-MT翻译引擎实现"""

    def __init__(self):
        settings = get_settings()
        self.api_key = settings.qwen_mt_api_key
        self.api_url = settings.qwen_mt_api_url
        self.model = settings.qwen_mt_model
        self.timeout = settings.qwen_mt_timeout
        self.max_tokens = settings.qwen_mt_max_tokens
        self.temperature = settings.qwen_mt_temperature
        self.max_concurrent = 5  # 默认并发数
        
        if not self.api_key:
            raise ValueError("Qwen3-MT配置不完整，请检查.env文件中的QWEN_MT_API_KEY")
        
        self._log_initialization()

        # 支持的语言映射
        self.languages = {
            "auto": "auto",
            "zh": "Chinese",
            "en": "English",
            "zh-tw": "Traditional Chinese",
            "ru": "Russian",
            "ja": "Japanese",
            "ko": "Korean",
            "es": "Spanish",
            "fr": "French",
            "pt": "Portuguese",
            "de": "German",
            "it": "Italian",
            "th": "Thai",
            "vie": "Vietnamese",
            "id": "Indonesian",
            "may": "Malay",
            "ar": "Arabic",
            "hi": "Hindi",
            "he": "Hebrew",
            "bur": "Burmese",
            "tam": "Tamil",
            "ur": "Urdu",
            "bn": "Bengali",
            "pl": "Polish",
            "nl": "Dutch",
            "ro": "Romanian",
            "tr": "Turkish",
            "hkm": "Khmer",
            "lao": "Lao",
            "yue": "Cantonese",
            "cs": "Czech",
            "el": "Greek",
            "sv": "Swedish",
            "hu": "Hungarian",
            "da": "Danish",
            "fi": "Finnish",
            "uk": "Ukrainian",
            "bg": "Bulgarian",
            "sr": "Serbian",
            "te": "Telugu",
            "af": "Afrikaans",
            "hy": "Armenian",
            "as": "Assamese",
            "ast": "Asturian",
            "eu": "Basque",
            "be": "Belarusian",
            "bs": "Bosnian",
            "ca": "Catalan",
            "ceb": "Cebuano",
            "hr": "Croatian",
            "arz": "Egyptian Arabic",
            "et": "Estonian",
            "gl": "Galician",
            "ka": "Georgian",
            "gu": "Gujarati",
            "is": "Icelandic",
            "jv": "Javanese",
            "kn": "Kannada",
            "kk": "Kazakh",
            "lv": "Latvian",
            "lt": "Lithuanian",
            "lb": "Luxembourgish",
            "mk": "Macedonian",
            "mai": "Maithili",
            "mt": "Maltese",
            "mr": "Marathi",
            "acm": "Mesopotamian Arabic",
            "ary": "Moroccan Arabic",
            "ars": "Najdi Arabic",
            "ne": "Nepali",
            "azj": "North Azerbaijani",
            "apc": "North Levantine Arabic",
            "uzn": "Northern Uzbek",
            "nb": "Norwegian Bokmål",
            "nn": "Norwegian Nynorsk",
            "oc": "Occitan",
            "or": "Odia",
            "pag": "Pangasinan",
            "scn": "Sicilian",
            "sd": "Sindhi",
            "si": "Sinhala",
            "sk": "Slovak",
            "sl": "Slovenian",
            "ajp": "South Levantine Arabic",
            "sw": "Swahili",
            "tl": "Tagalog",
            "acq": "Ta'izzi-Adeni Arabic",
            "als": "Tosk Albanian",
            "aeb": "Tunisian Arabic",
            "vec": "Venetian",
            "war": "Waray",
            "cy": "Welsh",
            "pes": "Western Persian"
        }

    async def translate_single(self, request: TranslationRequest) -> TranslationResponse:
        """翻译单个文本"""
        if not request.text.strip():
            return TranslationResponse(
                success=False,
                original=request.text,
                error="文本为空"
            )
        
        # 构建翻译选项
        translation_options = {
            "source_lang": self.languages.get(request.from_lang, "auto"),
            "target_lang": self.languages.get(request.to_lang, "English")
        }
        
        # 构建请求数据
        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": request.text}],
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "translation_options": translation_options
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(self.api_url, json=data, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        if "choices" in result and len(result["choices"]) > 0:
                            translated_text = result["choices"][0]["message"]["content"].strip()
                            return TranslationResponse(
                                success=True,
                                original=request.text,
                                translated=translated_text,
                                from_lang=request.from_lang,
                                to_lang=request.to_lang
                            )
                        else:
                            return TranslationResponse(
                                success=False,
                                original=request.text,
                                error="API响应格式错误"
                            )
                    else:
                        error_text = await response.text()
                        return TranslationResponse(
                            success=False,
                            original=request.text,
                            error=f"API请求失败: {response.status} - {error_text}"
                        )
        except Exception as e:
            return TranslationResponse(
                success=False,
                original=request.text,
                error=f"翻译请求异常: {str(e)}"
            )
    
    async def translate_text(self, text: str, from_lang: str, to_lang: str) -> Dict[str, Any]:
        """
        翻译文本（兼容旧接口）

        Args:
            text: 要翻译的文本
            from_lang: 源语言代码
            to_lang: 目标语言代码

        Returns:
            翻译结果字典
        """
        request = TranslationRequest(
            text=text,
            from_lang=from_lang,
            to_lang=to_lang
        )
        response = await self.translate_single(request)
        
        if response.success:
            return {
                "success": True,
                "translated_text": response.translated,
                "from_lang": response.from_lang,
                "to_lang": response.to_lang
            }
        else:
            return {
                "success": False,
                "error": response.error
            }
    
    async def translate_batch(
        self,
        texts: List[str],
        from_lang: str,
        to_lang: str,
        max_concurrent: int = None
    ) -> BatchTranslationResult:
        """批量翻译"""
        start_time = time.time()
        
        if max_concurrent is None:
            max_concurrent = self.max_concurrent
        
        if not texts:
            return BatchTranslationResult(
                translations=[],
                success_count=0,
                failed_count=0,
                total_count=0,
                unique_count=0,
                duration=0.0
            )
        
        # 并发翻译
        results = await self._concurrent_translate_texts(
            texts, from_lang, to_lang, max_concurrent
        )
        
        # 统计结果
        success_count = sum(1 for r in results if r.success)
        failed_count = len(results) - success_count
        duration = round(time.time() - start_time, 2)
        
        return BatchTranslationResult(
            translations=results,
            success_count=success_count,
            failed_count=failed_count,
            total_count=len(texts),
            unique_count=len(texts),
            duration=duration
        )
    
    async def _concurrent_translate_texts(
        self,
        texts: List[str],
        from_lang: str,
        to_lang: str,
        max_concurrent: int
    ) -> List[TranslationResponse]:
        """并发翻译文本列表"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def translate_with_semaphore(text):
            async with semaphore:
                request = TranslationRequest(
                    text=text,
                    from_lang=from_lang,
                    to_lang=to_lang
                )
                return await self.translate_single(request)
        
        results = await asyncio.gather(
            *[translate_with_semaphore(text) for text in texts],
            return_exceptions=True
        )
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(TranslationResponse(
                    success=False,
                    original=texts[i],
                    error=f"并发执行错误: {str(result)}"
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        return {
            "app_id": "qwen-mt",
            "secret_key_preview": f"{self.api_key[:8]}...{self.api_key[-4:]}" if self.api_key else "",
            "api_url": self.api_url,
            "timeout": self.timeout,
            "max_concurrent": self.max_concurrent
        }
    
    def validate_config(self) -> bool:
        """验证配置"""
        return bool(self.api_key and self.api_url)

    def _get_language_name(self, lang_code: str) -> str:
        """获取语言名称"""
        language_names = {
            "zh": "中文",
            "en": "英语",
            "may": "马来语",
            "hkm": "高棉语",
            "id": "印尼语",
            "bur": "缅甸语",
            "fil": "菲律宾语",
            "th": "泰语",
            "vie": "越南语",
            "tam": "泰米尔语",
            "lao": "老挝语",
            "auto": "自动检测"
        }
        return language_names.get(lang_code, f"未知语言({lang_code})")
    
    def _log_initialization(self):
        """记录初始化信息"""
        print(f"  TIMEOUT: {self.timeout}秒")
        print(f"  MODEL: {self.model}")


# 创建全局实例
try:
    qwen_mt_translation_engine = QwenMTTranslationEngine()
except ValueError as e:
    qwen_mt_translation_engine = None
    print(f"Qwen3-MT翻译引擎初始化失败: {e}")