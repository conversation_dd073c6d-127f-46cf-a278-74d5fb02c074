"""
缓存服务实现
缓存逻辑抽取到服务层
"""

from typing import Dict, Any, Optional
from app.interfaces.services.translation_service_interface import ICacheService
from app.services.adapters.cache_service_adapter import (
    file_cache_service_adapter,
    redis_cache_service_adapter
)


class FileCacheServiceImpl(ICacheService):
    """文件缓存服务实现"""

    def __init__(self):
        self.cache_service = file_cache_service_adapter

    async def get_cache(self, path: str, source_lang: str, target_lang: str) -> Optional[Dict[str, Any]]:
        """获取文件缓存"""
        if not self.cache_service:
            return None
        return await self.cache_service.get_cache(path, source_lang, target_lang)

    async def set_cache(self, path: str, source_lang: str, target_lang: str, data: Dict[str, Any]) -> bool:
        """设置文件缓存"""
        if not self.cache_service:
            return False
        return await self.cache_service.set_cache(path, source_lang, target_lang, data)

    async def clear_cache(self, path: str, source_lang: str, target_lang: str) -> bool:
        """清除文件缓存"""
        if not self.cache_service:
            return False
        return await self.cache_service.delete_cache(path, source_lang, target_lang)


class RedisCacheServiceImpl(ICacheService):
    """Redis缓存服务实现"""

    def __init__(self):
        self.cache_service = redis_cache_service_adapter

    async def get_cache(self, path: str, source_lang: str, target_lang: str) -> Optional[Dict[str, Any]]:
        """获取Redis缓存"""
        if not self.cache_service:
            return None
        return await self.cache_service.get_cache(path, source_lang, target_lang)

    async def set_cache(self, path: str, source_lang: str, target_lang: str, data: Dict[str, Any]) -> bool:
        """设置Redis缓存"""
        if not self.cache_service:
            return False
        return await self.cache_service.set_cache(path, source_lang, target_lang, data)

    async def clear_cache(self, path: str, source_lang: str, target_lang: str) -> bool:
        """清除Redis缓存"""
        if not self.cache_service:
            return False
        return await self.cache_service.delete_cache(path, source_lang, target_lang)


class CacheServiceFactory:
    """缓存服务工厂"""

    @staticmethod
    def create_file_cache() -> ICacheService:
        """创建文件缓存服务"""
        return FileCacheServiceImpl()

    @staticmethod
    def create_redis_cache() -> ICacheService:
        """创建Redis缓存服务"""
        return RedisCacheServiceImpl()


# 创建全局实例
file_cache_impl = FileCacheServiceImpl()
redis_cache_impl = RedisCacheServiceImpl()
