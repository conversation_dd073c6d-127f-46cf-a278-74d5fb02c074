"""
内容哈希缓存服务
基于内容生成哈希值作为缓存键，解决动态数据缓存问题
"""

import hashlib
import time
import json
from typing import Optional, Dict, Any, Union
from app.models.entities.cache_entity import <PERSON>acheKey, CacheEntry
from app.models.enums.translation_enums import LanguageCode, CacheStrategy
from app.services.infrastructure.file_cache_service import file_cache_service_v2
from app.services.infrastructure.redis_cache_service import redis_cache_service_v2


class ContentHashCacheService:
    """内容哈希缓存服务"""
    
    def __init__(self):
        self.file_cache = file_cache_service_v2
        self.redis_cache = redis_cache_service_v2
    
    def generate_content_hash(self, content: str, target_language: str) -> str:
        """基于内容和目标语言生成哈希值"""
        # 将内容和目标语言组合
        combined = f"{content}_{target_language}"
        # 生成MD5哈希
        return hashlib.md5(combined.encode('utf-8')).hexdigest()
    
    def generate_cache_key(self, path: str, content: str, target_language: str, 
                          cache_strategy: str = "auto") -> str:
        """智能缓存键生成"""
        
        if cache_strategy == "content_hash":
            # 基于内容哈希
            content_hash = self.generate_content_hash(content, target_language)
            return f"content_{content_hash}"
        
        elif cache_strategy == "path_time":
            # 基于路径+时间（5分钟为单位）
            timestamp = int(time.time() / 300)
            return f"path_{path}_{target_language}_{timestamp}"
        
        elif cache_strategy == "auto":
            # 自动判断：如果内容较小使用内容哈希，较大使用路径+时间
            if len(content) < 10000:  # 10KB以下
                return self.generate_cache_key(path, content, target_language, "content_hash")
            else:
                return self.generate_cache_key(path, content, target_language, "path_time")
        
        else:
            # 默认路径缓存
            return f"path_{path}_{target_language}"
    
    async def get_cache_by_content(self, path: str, content: str, source_lang: str,
                                  target_lang: str, cache_strategy: str = "auto",
                                  use_redis: bool = False) -> Optional[Dict[str, Any]]:
        """基于内容获取缓存"""
        try:
            # 生成统一的缓存键
            cache_key_str = self._generate_unified_cache_key(path, content, target_lang, cache_strategy)

            # 创建CacheKey对象
            cache_key = CacheKey(
                path=cache_key_str,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )

            # 直接使用缓存存储，绕过旧的键生成器
            if use_redis:
                storage = self.redis_cache.storage
                serializer = self.redis_cache.serializer
                compressor = self.redis_cache.compressor
            else:
                storage = self.file_cache.storage
                serializer = self.file_cache.serializer
                compressor = self.file_cache.compressor

            # 直接从存储获取
            raw_data = await storage.get(cache_key_str)
            if raw_data:
                # 解压和反序列化
                if compressor and len(raw_data) > 1024:
                    raw_data = compressor.decompress(raw_data)
                cache_entry = serializer.deserialize(raw_data)
                result = cache_entry.content
            else:
                result = None

            if result:
                return result
            else:
                return None

        except Exception as e:
            return None
    
    async def set_cache_by_content(self, path: str, content: str, source_lang: str,
                                  target_lang: str, data: Dict[str, Any],
                                  cache_strategy: str = "auto", ttl_seconds: int = 3600,
                                  use_redis: bool = False) -> bool:
        """基于内容设置缓存"""
        try:
            # 生成统一的缓存键
            cache_key_str = self._generate_unified_cache_key(path, content, target_lang, cache_strategy)

            # 创建CacheKey对象
            cache_key = CacheKey(
                path=cache_key_str,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )

            # 直接使用缓存存储，绕过旧的键生成器
            if use_redis:
                storage = self.redis_cache.storage
                serializer = self.redis_cache.serializer
                compressor = self.redis_cache.compressor
                metadata_builder = self.redis_cache.metadata_builder
            else:
                storage = self.file_cache.storage
                serializer = self.file_cache.serializer
                compressor = self.file_cache.compressor
                metadata_builder = self.file_cache.metadata_builder

            # 创建缓存条目
            if use_redis:
                base_config = self.redis_cache.config
                cache_method = "redis_content_hash"
            else:
                base_config = self.file_cache.config
                cache_method = "file_content_hash"

            # 创建临时配置对象，设置TTL
            temp_config = type('Config', (), {
                'ttl_seconds': ttl_seconds,
                **{k: v for k, v in base_config.__dict__.items() if k != 'ttl_seconds'}
            })()

            metadata = metadata_builder.build_metadata(
                cache_key, cache_key_str, temp_config, cache_method
            )
            cache_entry = CacheEntry(metadata=metadata, content=data)

            # 序列化和压缩
            serialized_data = serializer.serialize(cache_entry)
            if compressor and len(serialized_data) > 1024:
                serialized_data = compressor.compress(serialized_data)

            # 直接保存到存储
            success = await storage.set(cache_key_str, serialized_data, ttl_seconds)
            result = type('Result', (), {'success': success})()

            if result.success:
                return True
            else:
                return False

        except Exception as e:
            return False
    
    async def clear_cache_by_content(self, path: str, content: str, source_lang: str,
                                    target_lang: str, cache_strategy: str = "auto",
                                    use_redis: bool = False) -> bool:
        """基于内容清除缓存"""
        try:
            # 生成统一的缓存键
            cache_key_str = self._generate_unified_cache_key(path, content, target_lang, cache_strategy)

            # 创建CacheKey对象
            cache_key = CacheKey(
                path=cache_key_str,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )

            # 直接使用缓存存储，绕过旧的键生成器
            if use_redis:
                storage = self.redis_cache.storage
            else:
                storage = self.file_cache.storage

            # 直接从存储删除
            success = await storage.delete(cache_key_str)
            result = type('Result', (), {'success': success})()

            if result.success:
                print(f"🗑️ 缓存已清除: {cache_key_str[:20]}...")
                return True
            else:
                return False

        except Exception as e:
            return False
    
    def _generate_unified_cache_key(self, path: str, content: str, target_language: str,
                                   cache_strategy: str = "auto") -> str:
        """统一的缓存键生成 - 解决双重哈希问题"""

        if cache_strategy == "content_hash" or cache_strategy == "force_content_hash":
            # 基于内容哈希（force_content_hash强制使用，无视大小限制）
            content_hash = self.generate_content_hash(content, target_language)
            return content_hash  # 直接返回哈希，不加前缀

        elif cache_strategy == "path_time":
            # 基于路径+时间（改为24小时窗口，大幅减少重复翻译）
            timestamp = int(time.time() / 86400)  # 86400秒 = 24小时
            return f"{path}_{target_language}_{timestamp}"

        elif cache_strategy == "auto":
            # 终极方案：所有内容都使用content_hash，永不重复翻译
            return self._generate_unified_cache_key(path, content, target_language, "content_hash")

        elif cache_strategy == "path_content_hash":
            # 路径+内容混合哈希，既考虑路径又考虑内容，但不依赖时间
            combined = f"{path}_{content}_{target_language}"
            return hashlib.md5(combined.encode('utf-8')).hexdigest()

        else:
            # 默认路径缓存（不推荐，仍可能重复翻译）
            return f"{path}_{target_language}"

    def _is_static_content(self, path: str) -> bool:
        """判断是否为静态内容"""
        static_indicators = [
            '/about', '/contact', '/privacy', '/terms',
            '/help', '/faq', '/documentation', '/guide',
            '.html', '.htm', '/static/', '/assets/'
        ]
        path_lower = path.lower()
        return any(indicator in path_lower for indicator in static_indicators)

    def get_cache_info(self, path: str, content: str, target_lang: str,
                      cache_strategy: str = "auto") -> Dict[str, Any]:
        """获取缓存信息"""
        cache_key = self._generate_unified_cache_key(path, content, target_lang, cache_strategy)
        content_hash = self.generate_content_hash(content, target_lang)

        return {
            "cache_strategy": cache_strategy,
            "cache_key": cache_key,
            "content_hash": content_hash,
            "content_size": len(content),
            "is_static": self._is_static_content(path),
            "recommended_strategy": "content_hash" if len(content) < 500000 else "path_time"
        }


# 创建全局实例
content_hash_cache_service = ContentHashCacheService()
