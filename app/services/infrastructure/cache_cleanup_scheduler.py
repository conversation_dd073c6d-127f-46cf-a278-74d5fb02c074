"""
缓存清理定时任务调度器
根据 .env 配置的时间间隔自动清理缓存文件
"""

import asyncio
import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Optional

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from app.config.config import get_settings


class CacheCleanupScheduler:
    """缓存清理定时任务调度器"""
    
    def __init__(self):
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)

        # 从配置读取清理间隔（分钟）
        self.translation_cleanup_interval = self.settings.translation_cache_cleanup_interval_minutes
        self.image_cleanup_interval = self.settings.image_cache_cleanup_interval_minutes

        # 缓存目录路径
        self.translations_cache_dir = Path("cache/translations")
        self.images_cache_dir = Path("cache/img")
        
    async def initialize(self) -> bool:
        """初始化调度器"""
        try:
            # 创建异步调度器
            self.scheduler = AsyncIOScheduler()

            # 添加翻译缓存清理任务
            self.scheduler.add_job(
                func=self._cleanup_translations_cache,
                trigger=IntervalTrigger(minutes=self.translation_cleanup_interval),
                id='translation_cache_cleanup',
                name=f'翻译缓存清理任务(每{self.translation_cleanup_interval}分钟)',
                replace_existing=True,
                max_instances=1
            )

            # 添加图片缓存清理任务
            self.scheduler.add_job(
                func=self._cleanup_images_cache,
                trigger=IntervalTrigger(minutes=self.image_cleanup_interval),
                id='image_cache_cleanup',
                name=f'图片缓存清理任务(每{self.image_cleanup_interval}分钟)',
                replace_existing=True,
                max_instances=1
            )
            
            # 启动调度器
            self.scheduler.start()
            
            self.logger.info(f"🕛 缓存清理调度器已启动")
            self.logger.info(f"   📄 翻译缓存清理: 每{self.translation_cleanup_interval}分钟")
            self.logger.info(f"   📷 图片缓存清理: 每{self.image_cleanup_interval}分钟")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 缓存清理调度器初始化失败: {e}")
            return False
    


    async def _cleanup_translations_cache(self):
        """清理翻译缓存文件"""
        try:
            if not self.translations_cache_dir.exists():
                self.logger.info("📁 翻译缓存目录不存在，跳过清理")
                return

            # 统计清理前的文件信息（递归查找所有文件）
            all_items = list(self.translations_cache_dir.rglob("*"))
            cache_files = [item for item in all_items if item.is_file()]

            total_files = len(cache_files)

            if total_files == 0:
                self.logger.info("📂 翻译缓存目录为空，无需清理")
                return

            # 计算总大小
            total_size_bytes = sum(f.stat().st_size for f in cache_files if f.exists())
            total_size_mb = total_size_bytes / (1024 * 1024)

            self.logger.info(f"📊 发现 {total_files} 个翻译缓存文件，总大小: {total_size_mb:.2f} MB")

            # 删除所有翻译缓存文件
            deleted_count = 0
            failed_count = 0

            for cache_file in cache_files:
                try:
                    if cache_file.exists():
                        cache_file.unlink()
                        deleted_count += 1
                except Exception as e:
                    failed_count += 1
                    self.logger.warning(f"⚠️ 删除翻译缓存文件失败 {cache_file.name}: {e}")

            # 尝试删除空的子目录
            await self._cleanup_empty_directories(self.translations_cache_dir)

            # 记录清理结果
            self.logger.info(f"✅ 翻译缓存清理完成!")
            self.logger.info(f"   📈 删除文件: {deleted_count}/{total_files}")
            self.logger.info(f"   💾 释放空间: {total_size_mb:.2f} MB")

            if failed_count > 0:
                self.logger.warning(f"   ⚠️ 失败文件: {failed_count}")

        except Exception as e:
            self.logger.error(f"❌ 翻译缓存清理失败: {e}")

    async def _cleanup_images_cache(self):
        """清理图片缓存文件"""
        try:
            if not self.images_cache_dir.exists():
                self.logger.info("📁 图片缓存目录不存在，跳过清理")
                return

            # 获取所有图片缓存文件
            cache_files = list(self.images_cache_dir.glob("*.json"))
            total_files = len(cache_files)

            if total_files == 0:
                self.logger.info("📂 图片缓存目录为空，无需清理")
                return

            # 计算总大小
            total_size_bytes = sum(f.stat().st_size for f in cache_files if f.exists())
            total_size_mb = total_size_bytes / (1024 * 1024)

            self.logger.info(f"📊 发现 {total_files} 个图片缓存文件，总大小: {total_size_mb:.2f} MB")

            # 删除所有图片缓存文件
            deleted_count = 0
            failed_count = 0

            for cache_file in cache_files:
                try:
                    if cache_file.exists():
                        cache_file.unlink()
                        deleted_count += 1
                except Exception as e:
                    failed_count += 1
                    self.logger.warning(f"⚠️ 删除图片缓存文件失败 {cache_file.name}: {e}")

            # 记录清理结果
            self.logger.info(f"✅ 图片缓存清理完成!")
            self.logger.info(f"   📈 删除文件: {deleted_count}/{total_files}")
            self.logger.info(f"   💾 释放空间: {total_size_mb:.2f} MB")

            if failed_count > 0:
                self.logger.warning(f"   ⚠️ 失败文件: {failed_count}")

        except Exception as e:
            self.logger.error(f"❌ 图片缓存清理失败: {e}")
    
    async def _cleanup_empty_directories(self, cache_dir: Path):
        """清理空的子目录"""
        try:
            for subdir in cache_dir.iterdir():
                if subdir.is_dir():
                    try:
                        # 检查目录是否为空
                        if not any(subdir.iterdir()):
                            subdir.rmdir()
                            self.logger.debug(f"🗂️ 删除空目录: {subdir.name}")
                    except Exception as e:
                        self.logger.debug(f"删除空目录失败 {subdir.name}: {e}")
        except Exception as e:
            self.logger.debug(f"清理空目录时出错: {e}")
    
    async def manual_cleanup(self) -> dict:
        """手动触发清理任务"""
        try:
            self.logger.info("🔧 手动触发缓存清理任务...")
            await self.cleanup_all_cache_files()
            return {
                "success": True,
                "message": "手动清理任务执行完成",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            error_msg = f"手动清理任务执行失败: {e}"
            self.logger.error(f"❌ {error_msg}")
            return {
                "success": False,
                "message": error_msg,
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_next_cleanup_time(self) -> Optional[str]:
        """获取下次清理时间"""
        try:
            if self.scheduler:
                job = self.scheduler.get_job('daily_cache_cleanup')
                if job and job.next_run_time:
                    return job.next_run_time.strftime("%Y-%m-%d %H:%M:%S")
            return None
        except Exception as e:
            self.logger.error(f"获取下次清理时间失败: {e}")
            return None
    
    async def get_cache_info(self) -> dict:
        """获取缓存目录信息"""
        try:
            if not self.cache_dir.exists():
                return {
                    "exists": False,
                    "total_files": 0,
                    "total_size_mb": 0.0
                }
            
            cache_files = list(self.cache_dir.glob("*.cache"))
            total_files = len(cache_files)
            
            if total_files == 0:
                total_size_mb = 0.0
            else:
                total_size_bytes = sum(f.stat().st_size for f in cache_files if f.exists())
                total_size_mb = total_size_bytes / (1024 * 1024)
            
            return {
                "exists": True,
                "total_files": total_files,
                "total_size_mb": round(total_size_mb, 2),
                "cache_dir": str(self.cache_dir),
                "next_cleanup": await self.get_next_cleanup_time()
            }
            
        except Exception as e:
            self.logger.error(f"获取缓存信息失败: {e}")
            return {
                "exists": False,
                "total_files": 0,
                "total_size_mb": 0.0,
                "error": str(e)
            }
    
    async def shutdown(self):
        """关闭调度器"""
        try:
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown(wait=True)
                self.logger.info("🛑 缓存清理调度器已关闭")
        except Exception as e:
            self.logger.error(f"❌ 关闭缓存清理调度器失败: {e}")


# 创建全局实例
cache_cleanup_scheduler = CacheCleanupScheduler()
