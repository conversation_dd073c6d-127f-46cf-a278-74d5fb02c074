"""
Redis缓存服务实现 - 重构后的版本
按照五层架构重构的Redis缓存服务
"""

import redis.asyncio as redis
from typing import Optional, Dict, Any, List
from app.interfaces.services.cache_service_interface import ICacheService, ICacheStorage
from app.models.entities.cache_entity import (
    CacheKey,
    CacheEntry,
    RedisCacheConfig,
    CacheStatistics,
    CacheOperationResult
)
from app.models.enums.translation_enums import CacheStrategy
from app.services.infrastructure.cache_utils_service import (
    cache_key_generator,
    cache_serializer,
    cache_compressor,
    cache_metadata_builder
)
from app.config.config import get_settings


class RedisStorage(ICacheStorage):
    """Redis存储实现"""

    def __init__(self, config: RedisCacheConfig):
        self.config = config
        self.redis_client = None

    async def initialize(self) -> bool:
        """初始化Redis连接"""
        try:
            self.redis_client = redis.Redis(
                host=self.config.host,
                port=self.config.port,
                db=self.config.db,
                password=self.config.password,
                decode_responses=False  # 保持二进制数据用于压缩
            )

            # 测试连接
            await self.redis_client.ping()
            return True

        except Exception as e:
            return False

    async def get(self, key: str) -> Optional[bytes]:
        """获取缓存数据"""
        if not self.redis_client:
            return None

        try:
            return await self.redis_client.get(key)
        except Exception as e:
            return None

    async def set(self, key: str, data: bytes, ttl_seconds: Optional[int] = None) -> bool:
        """设置缓存数据"""
        if not self.redis_client:
            return False

        try:
            if ttl_seconds:
                await self.redis_client.set(key, data, ex=ttl_seconds)
            else:
                await self.redis_client.set(key, data)
            return True
        except Exception as e:
            return False

    async def delete(self, key: str) -> bool:
        """删除缓存数据"""
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.delete(key)
            return bool(result)
        except Exception as e:
            return False

    async def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self.redis_client:
            return False

        try:
            result = await self.redis_client.exists(key)
            return bool(result)
        except Exception as e:
            return False

    async def clear_all(self) -> bool:
        """清空所有缓存"""
        if not self.redis_client:
            return False

        try:
            pattern = "r:*"
            keys = await self.redis_client.keys(pattern)
            if keys:
                await self.redis_client.delete(*keys)
                print(f"🗑️ 已清空 {len(keys)} 个Redis缓存")
            return True
        except Exception as e:
            return False

    async def get_info(self) -> Dict[str, Any]:
        """获取Redis信息"""
        if not self.redis_client:
            return {"status": "disconnected"}

        try:
            info = await self.redis_client.info()
            pattern = "r:*"
            keys = await self.redis_client.keys(pattern)

            return {
                "status": "connected",
                "redis_version": info.get('redis_version', 'unknown'),
                "connected_clients": info.get('connected_clients', 0),
                "used_memory_human": info.get('used_memory_human', 'unknown'),
                "keyspace_hits": info.get('keyspace_hits', 0),
                "keyspace_misses": info.get('keyspace_misses', 0),
                "total_keys": len(keys)
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()


class RedisCacheServiceV2(ICacheService):
    """Redis缓存服务实现 - 重构版本"""

    def __init__(self):
        """初始化Redis缓存服务"""
        settings = get_settings()

        # 创建配置实体
        self.config = RedisCacheConfig(
            strategy=CacheStrategy.REDIS_CACHE,
            ttl_seconds=settings.cache_ttl,
            host=settings.redis_host,
            port=settings.redis_port,
            db=settings.redis_db,
            password=settings.redis_password,
            compression_enabled=True,
            compression_min_size=1024
        )

        # 初始化组件
        self.storage = RedisStorage(self.config)
        self.key_generator = cache_key_generator
        self.serializer = cache_serializer
        self.compressor = cache_compressor
        self.metadata_builder = cache_metadata_builder

        pass

    async def initialize(self) -> bool:
        """初始化缓存服务"""
        return await self.storage.initialize()

    async def get_cache(self, cache_key: CacheKey) -> Optional[Dict[str, Any]]:
        """获取缓存"""
        # 验证缓存键
        if not self.key_generator.validate_key(cache_key):
            return None

        # 生成Redis键
        redis_key = self.key_generator.generate_key(cache_key)

        # 从存储获取数据
        raw_data = await self.storage.get(redis_key)
        if raw_data is None:
            return None

        try:
            # 解压数据
            decompressed_data = self.compressor.decompress(raw_data)

            # 反序列化
            cache_entry = self.serializer.deserialize(decompressed_data)

            # 检查是否过期
            if not cache_entry.is_valid:
                await self.storage.delete(redis_key)
                return None

            return cache_entry.content

        except Exception as e:
            return None

    async def set_cache(self, cache_key: CacheKey, data: Dict[str, Any]) -> CacheOperationResult:
        """设置缓存"""
        # 验证缓存键
        if not self.key_generator.validate_key(cache_key):
            return CacheOperationResult.error_result("无效的缓存键")

        # 生成Redis键
        redis_key = self.key_generator.generate_key(cache_key)

        try:
            # 构建缓存条目
            metadata = self.metadata_builder.build_metadata(
                cache_key, redis_key, self.config, "redis_v2"
            )
            cache_entry = CacheEntry(metadata=metadata, content=data)

            # 序列化
            serialized_data = self.serializer.serialize(cache_entry)

            # 压缩（如果需要）
            if self.config.compression_enabled and self.compressor.should_compress(serialized_data):
                final_data = self.compressor.compress(serialized_data)
            else:
                final_data = serialized_data

            # 保存到存储
            success = await self.storage.set(redis_key, final_data, self.config.ttl_seconds)

            if success:
                return CacheOperationResult.success_result("缓存保存成功", {"key": redis_key})
            else:
                return CacheOperationResult.error_result("缓存保存失败")

        except Exception as e:
            return CacheOperationResult.error_result("缓存保存异常", str(e))

    async def delete_cache(self, cache_key: CacheKey) -> CacheOperationResult:
        """删除缓存"""
        # 生成Redis键
        redis_key = self.key_generator.generate_key(cache_key)

        try:
            success = await self.storage.delete(redis_key)
            if success:
                print(f"🗑️ Redis缓存已删除: {redis_key}")
                return CacheOperationResult.success_result("缓存删除成功")
            else:
                return CacheOperationResult.error_result("缓存不存在或删除失败")
        except Exception as e:
            return CacheOperationResult.error_result("缓存删除异常", str(e))

    async def exists_cache(self, cache_key: CacheKey) -> bool:
        """检查缓存是否存在"""
        redis_key = self.key_generator.generate_key(cache_key)
        return await self.storage.exists(redis_key)

    async def get_statistics(self) -> CacheStatistics:
        """获取缓存统计"""
        try:
            info = await self.storage.get_info()

            return CacheStatistics(
                total_entries=info.get("total_keys", 0),
                total_size_mb=0.0,  # Redis不直接提供大小信息
                hit_count=info.get("keyspace_hits", 0),
                miss_count=info.get("keyspace_misses", 0)
            )
        except Exception:
            return CacheStatistics(total_entries=0, total_size_mb=0.0)

    async def clear_all_cache(self) -> CacheOperationResult:
        """清空所有缓存"""
        try:
            success = await self.storage.clear_all()
            if success:
                return CacheOperationResult.success_result("所有缓存已清空")
            else:
                return CacheOperationResult.error_result("清空缓存失败")
        except Exception as e:
            return CacheOperationResult.error_result("清空缓存异常", str(e))

    async def close(self):
        """关闭缓存服务"""
        await self.storage.close()


# 创建全局实例
redis_cache_service_v2 = RedisCacheServiceV2()
