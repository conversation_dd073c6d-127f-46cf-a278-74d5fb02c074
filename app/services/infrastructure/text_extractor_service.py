"""
文本提取器服务实现
从原有DOM服务中抽取的文本提取逻辑
"""

import re
from typing import List, Dict
from app.interfaces.services.dom_service_interface import ITextExtractor
from app.models.entities.dom_entity import (
    DomExtractionData,
    DomProcessingConfig,
    TextNodeInfo
)
from app.services.infrastructure.dom_parser_service import dom_parser_service


class TextExtractorService(ITextExtractor):
    """文本提取器服务实现"""

    def __init__(self):
        self.dom_parser = dom_parser_service

    def extract_chinese_texts(self, html_content: str, config: DomProcessingConfig) -> DomExtractionData:
        """提取中文文本"""

        # 记录配置信息
        self._log_extraction_config(config)

        # 解析HTML
        soup = self.dom_parser.parse_html(html_content)

        # 提取文本节点
        text_nodes = self.dom_parser.extract_text_nodes(soup, config)

        # 收集所有中文文本
        chinese_texts = []
        for node in text_nodes:
            chinese_texts.extend(node.chinese_texts)

        # 构建统计信息
        statistics = {
            'total_text_nodes': len(text_nodes),
            'total_chinese_segments': len(chinese_texts),
            'unique_chinese_texts': len(set(chinese_texts))
        }

        # 创建提取数据实体
        extraction_data = DomExtractionData(
            soup=soup,
            text_nodes=text_nodes,
            chinese_texts=chinese_texts,
            statistics=statistics
        )

        self._log_extraction_result(extraction_data)
        return extraction_data

    def find_chinese_in_text(self, text: str) -> List[str]:
        """在文本中查找中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        return chinese_pattern.findall(text)

    def extract_remaining_chinese(self, html_content: str, exclude_comments: bool = True) -> List[str]:
        """提取剩余的中文文本"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')

        if exclude_comments:
            # 移除HTML注释后再提取
            comment_pattern = r'<!--(.*?)-->'
            html_without_comments = re.sub(comment_pattern, '', html_content, flags=re.DOTALL)
            return chinese_pattern.findall(html_without_comments)
        else:
            return chinese_pattern.findall(html_content)

    def extract_chinese_from_comments(self, html_content: str) -> List[str]:
        """从HTML注释中提取中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        comment_pattern = r'<!--(.*?)-->'

        chinese_in_comments = []
        comments = re.findall(comment_pattern, html_content, re.DOTALL)
        for comment in comments:
            chinese_in_comments.extend(chinese_pattern.findall(comment))

        return chinese_in_comments

    def extract_chinese_from_scripts(self, html_content: str) -> List[str]:
        """从JavaScript代码中提取中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        script_pattern = r'<script[^>]*>(.*?)</script>'

        chinese_in_scripts = []
        scripts = re.findall(script_pattern, html_content, re.DOTALL | re.IGNORECASE)
        for script in scripts:
            chinese_in_scripts.extend(chinese_pattern.findall(script))

        return chinese_in_scripts

    def extract_chinese_from_styles(self, html_content: str) -> List[str]:
        """从CSS样式中提取中文"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        style_pattern = r'<style[^>]*>(.*?)</style>'

        chinese_in_styles = []
        styles = re.findall(style_pattern, html_content, re.DOTALL | re.IGNORECASE)
        for style in styles:
            chinese_in_styles.extend(chinese_pattern.findall(style))

        return chinese_in_styles

    def get_text_statistics(self, text_nodes: List[TextNodeInfo]) -> Dict[str, int]:
        """获取文本统计信息"""
        total_chinese = 0
        attribute_texts = 0
        content_texts = 0

        for node in text_nodes:
            total_chinese += len(node.chinese_texts)
            if node.is_attribute_text:
                attribute_texts += 1
            else:
                content_texts += 1

        return {
            'total_nodes': len(text_nodes),
            'total_chinese_segments': total_chinese,
            'attribute_text_nodes': attribute_texts,
            'content_text_nodes': content_texts,
            'unique_chinese_texts': len(set(
                chinese for node in text_nodes for chinese in node.chinese_texts
            ))
        }

    def _log_extraction_config(self, config: DomProcessingConfig):
        """记录提取配置"""
        pass

    def _log_extraction_result(self, extraction_data: DomExtractionData):
        """记录提取结果"""
        pass


# 创建全局实例
text_extractor_service = TextExtractorService()
