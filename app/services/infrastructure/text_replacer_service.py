"""
文本替换器服务实现
从原有DOM服务中抽取的替换逻辑
"""

import re
from typing import Dict, <PERSON><PERSON>, List
from bs4 import BeautifulSoup, NavigableString, Comment
from app.interfaces.services.dom_service_interface import ITextReplacer
from app.models.entities.dom_entity import (
    DomExtractionData,
    DomProcessingConfig,
    TextNodeInfo
)
from app.services.infrastructure.dom_parser_service import selector_processor_service


class TextReplacerService(ITextReplacer):
    """文本替换器服务实现"""

    def replace_in_dom(self, dom_data: DomExtractionData, translation_map: Dict[str, str]) -> str:
        """在DOM中精确替换中文文本"""
        pass

        soup = dom_data.soup
        text_nodes = dom_data.text_nodes
        replacement_count = 0

        # 处理每个文本节点
        for node_info in text_nodes:
            node_replacements = self._replace_in_text_node(node_info, translation_map)
            replacement_count += node_replacements

        pass
        return str(soup)

    def replace_special_cases(self, html_content: str, translation_map: Dict[str, str]) -> <PERSON><PERSON>[str, int]:
        """处理特殊情况的替换"""
        pass

        updated_html = html_content
        special_replacements = 0

        # 1. 处理JavaScript中的中文
        updated_html, js_replacements = self._replace_in_javascript(updated_html, translation_map)
        special_replacements += js_replacements

        # 2. 处理CSS中的中文
        updated_html, css_replacements = self._replace_in_css(updated_html, translation_map)
        special_replacements += css_replacements

        pass
        return updated_html, special_replacements

    def brute_force_replace(
        self,
        html_content: str,
        translation_map: Dict[str, str],
        config: DomProcessingConfig
    ) -> Tuple[str, int]:
        """暴力替换"""
        pass

        if config.has_tag_filter:
            return self._brute_force_with_filter(html_content, translation_map, config)
        else:
            return self._brute_force_without_filter(html_content, translation_map)

    def _replace_in_text_node(self, node_info: TextNodeInfo, translation_map: Dict[str, str]) -> int:
        """在单个文本节点中进行替换"""
        content = node_info.content
        chinese_texts = node_info.chinese_texts
        element = node_info.element
        path = node_info.path

        new_content = content
        node_replacements = 0

        # 按长度从长到短排序，避免短文本干扰长文本
        sorted_chinese = sorted(chinese_texts, key=len, reverse=True)

        for chinese_text in sorted_chinese:
            if chinese_text in translation_map:
                translated_text = translation_map[chinese_text]
                if chinese_text in new_content:
                    new_content = new_content.replace(chinese_text, translated_text)
                    node_replacements += 1

        # 如果内容有变化，更新DOM
        if node_replacements > 0 and new_content != content:
            if node_info.is_attribute_text:
                # 更新属性值
                element[node_info.attr_name] = new_content
            else:
                # 更新文本节点
                element.replace_with(new_content)

        return node_replacements

    def _replace_in_javascript(self, html_content: str, translation_map: Dict[str, str]) -> Tuple[str, int]:
        """在JavaScript中替换中文"""
        js_replacements = 0

        def replace_js_chinese(match):
            nonlocal js_replacements
            js_content = match.group(1)
            original_js = js_content

            # 在JavaScript字符串中查找并替换中文
            for chinese, translated in translation_map.items():
                # 处理单引号字符串
                pattern = f"'([^']*{re.escape(chinese)}[^']*)'"
                def replace_single_quote(m):
                    return f"'{m.group(1).replace(chinese, translated)}'"
                js_content = re.sub(pattern, replace_single_quote, js_content)

                # 处理双引号字符串
                pattern = f'"([^"]*{re.escape(chinese)}[^"]*)"'
                def replace_double_quote(m):
                    return f'"{m.group(1).replace(chinese, translated)}"'
                js_content = re.sub(pattern, replace_double_quote, js_content)

            if js_content != original_js:
                js_replacements += 1

            return f"<script{match.group(0)[7:-9]}{js_content}</script>"

        # 处理script标签
        script_pattern = r'<script[^>]*>(.*?)</script>'
        updated_html = re.sub(script_pattern, replace_js_chinese, html_content, flags=re.DOTALL)

        return updated_html, js_replacements

    def _replace_in_css(self, html_content: str, translation_map: Dict[str, str]) -> Tuple[str, int]:
        """在CSS中替换中文"""
        css_replacements = 0

        def replace_css_chinese(match):
            nonlocal css_replacements
            css_content = match.group(1)
            original_css = css_content

            # 在CSS content属性中查找并替换中文
            for chinese, translated in translation_map.items():
                # 处理content属性
                pattern = f'content:\\s*["\']([^"\']*{re.escape(chinese)}[^"\']*)["\']'
                def replace_content(m):
                    return f'content: "{m.group(1).replace(chinese, translated)}"'
                css_content = re.sub(pattern, replace_content, css_content)

            if css_content != original_css:
                css_replacements += 1

            return f"<style{match.group(0)[6:-8]}{css_content}</style>"

        # 处理style标签
        style_pattern = r'<style[^>]*>(.*?)</style>'
        updated_html = re.sub(style_pattern, replace_css_chinese, html_content, flags=re.DOTALL)

        return updated_html, css_replacements

    def _brute_force_with_filter(
        self,
        html_content: str,
        translation_map: Dict[str, str],
        config: DomProcessingConfig
    ) -> Tuple[str, int]:
        """带过滤器的暴力替换"""

        brute_replacements = 0
        soup = BeautifulSoup(html_content, 'html.parser')
        allowed_selectors = selector_processor_service.parse_selectors(config.untranslatable_tags)

        # 获取所有剩余的中文
        chinese_pattern = re.compile(config.chinese_pattern)
        all_chinese = chinese_pattern.findall(html_content)
        unique_remaining = list(set(all_chinese))

        for chinese_text in unique_remaining:
            if chinese_text in translation_map:
                translated_text = translation_map[chinese_text]

                # 只在允许的元素中进行替换
                def replace_in_allowed_elements(element):
                    nonlocal brute_replacements
                    if isinstance(element, NavigableString) and not isinstance(element, Comment):
                        if selector_processor_service.should_process_element(
                            element.parent, allowed_selectors, soup
                        ):
                            content = str(element)
                            if chinese_text in content:
                                new_content = content.replace(chinese_text, translated_text)
                                if new_content != content:
                                    element.replace_with(new_content)
                                    brute_replacements += content.count(chinese_text)
                    elif hasattr(element, 'children'):
                        for child in list(element.children):
                            replace_in_allowed_elements(child)

                replace_in_allowed_elements(soup)

        return str(soup), brute_replacements

    def _brute_force_without_filter(
        self,
        html_content: str,
        translation_map: Dict[str, str]
    ) -> Tuple[str, int]:
        """无过滤器的暴力替换"""
        brute_replacements = 0
        updated_html = html_content
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')

        # 先提取所有HTML注释中的中文，用于排除
        comment_pattern = r'<!--(.*?)-->'
        comments = re.findall(comment_pattern, updated_html, re.DOTALL)
        comment_chinese = set()
        for comment in comments:
            comment_chinese.update(chinese_pattern.findall(comment))

        # 获取所有中文，但排除注释中的
        all_chinese = chinese_pattern.findall(updated_html)
        remaining_chinese = []

        for chinese_text in set(all_chinese):
            # 检查这个中文是否只存在于注释中
            temp_html = re.sub(comment_pattern, '', updated_html, flags=re.DOTALL)
            if chinese_text in temp_html:
                remaining_chinese.append(chinese_text)

        unique_remaining = list(set(remaining_chinese))

        for chinese_text in unique_remaining:
            if chinese_text in translation_map:
                translated_text = translation_map[chinese_text]

                # 分段处理HTML，避免替换注释中的内容
                parts = re.split(comment_pattern, updated_html, flags=re.DOTALL)
                new_parts = []

                for i, part in enumerate(parts):
                    if i % 2 == 0:  # 非注释部分
                        count_before = part.count(chinese_text)
                        if count_before > 0:
                            part = part.replace(chinese_text, translated_text)
                            brute_replacements += count_before
                        new_parts.append(part)
                    else:  # 注释部分，保持原样
                        new_parts.append(f'<!--{part}-->')

                updated_html = ''.join(new_parts)

        return updated_html, brute_replacements


# 创建全局实例
text_replacer_service = TextReplacerService()
