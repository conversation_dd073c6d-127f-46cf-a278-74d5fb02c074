"""
DOM解析器服务实现
从原有DOM服务中抽取的解析逻辑
"""

import re
from typing import List, Dict, Any
from bs4 import BeautifulSoup, NavigableString, Tag, Comment
from app.interfaces.services.dom_service_interface import IDomParser, ISelectorProcessor
from app.models.entities.dom_entity import (
    DomProcessingConfig,
    TextNodeInfo,
    CssSelector
)


class DomParserService(IDomParser):
    """DOM解析器服务实现"""
    
    def parse_html(self, html_content: str) -> BeautifulSoup:
        """解析HTML内容"""
        return BeautifulSoup(html_content, 'html.parser')
    
    def extract_text_nodes(self, soup: BeautifulSoup, config: DomProcessingConfig) -> List[TextNodeInfo]:
        """提取文本节点"""
        text_nodes = []
        chinese_pattern = re.compile(config.chinese_pattern)
        selector_processor = SelectorProcessorService()
        
        # 解析允许的选择器
        allowed_selectors = []
        if config.has_tag_filter:
            allowed_selectors = selector_processor.parse_selectors(config.untranslatable_tags)
        
        def extract_from_element(element, path=""):
            """递归提取元素中的文本"""
            if isinstance(element, NavigableString):
                self._process_text_node(
                    element, path, text_nodes, chinese_pattern, 
                    allowed_selectors, selector_processor, soup, config
                )
            elif isinstance(element, Tag):
                self._process_tag_element(
                    element, path, text_nodes, chinese_pattern,
                    allowed_selectors, selector_processor, soup, config
                )
        
        # 开始提取
        extract_from_element(soup)
        return text_nodes
    
    def _process_text_node(
        self, 
        element: NavigableString, 
        path: str, 
        text_nodes: List[TextNodeInfo],
        chinese_pattern: re.Pattern,
        allowed_selectors: List[CssSelector],
        selector_processor: ISelectorProcessor,
        soup: BeautifulSoup,
        config: DomProcessingConfig
    ):
        """处理文本节点"""
        # 跳过HTML注释
        if isinstance(element, Comment) and not config.process_comments:
            return
        
        # 检查父元素是否允许处理
        if allowed_selectors and not selector_processor.should_process_element(
            element.parent, allowed_selectors, soup
        ):
            return
        
        # 处理文本内容
        text_content = str(element).strip()
        if text_content:
            chinese_matches = chinese_pattern.findall(text_content)
            if chinese_matches:
                text_info = TextNodeInfo(
                    content=text_content,
                    chinese_texts=chinese_matches,
                    path=path,
                    element=element,
                    parent=element.parent
                )
                text_nodes.append(text_info)
    
    def _process_tag_element(
        self,
        element: Tag,
        path: str,
        text_nodes: List[TextNodeInfo],
        chinese_pattern: re.Pattern,
        allowed_selectors: List[CssSelector],
        selector_processor: ISelectorProcessor,
        soup: BeautifulSoup,
        config: DomProcessingConfig
    ):
        """处理标签元素"""
        current_path = f"{path}/{element.name}" if path else element.name
        
        # 检查当前标签是否允许处理
        should_process = True
        if allowed_selectors:
            should_process = selector_processor.should_process_element(
                element, allowed_selectors, soup
            )
        
        # 处理标签属性中的中文
        if should_process and config.process_attributes:
            self._process_element_attributes(
                element, current_path, text_nodes, chinese_pattern
            )
        
        # 递归处理子元素
        for child in element.children:
            self._extract_from_element_recursive(child, current_path, text_nodes, chinese_pattern, allowed_selectors, selector_processor, soup, config)
    
    def _process_element_attributes(
        self,
        element: Tag,
        current_path: str,
        text_nodes: List[TextNodeInfo],
        chinese_pattern: re.Pattern
    ):
        """处理元素属性中的中文"""
        for attr_name, attr_value in element.attrs.items():
            if isinstance(attr_value, str):
                chinese_matches = chinese_pattern.findall(attr_value)
                if chinese_matches:
                    attr_info = TextNodeInfo(
                        content=attr_value,
                        chinese_texts=chinese_matches,
                        path=f"{current_path}@{attr_name}",
                        element=element,
                        attr_name=attr_name
                    )
                    text_nodes.append(attr_info)
    
    def _extract_from_element_recursive(
        self, 
        element, 
        path: str, 
        text_nodes: List[TextNodeInfo],
        chinese_pattern: re.Pattern,
        allowed_selectors: List[CssSelector],
        selector_processor: ISelectorProcessor,
        soup: BeautifulSoup,
        config: DomProcessingConfig
    ):
        """递归提取元素（重构后的方法）"""
        if isinstance(element, NavigableString):
            self._process_text_node(
                element, path, text_nodes, chinese_pattern,
                allowed_selectors, selector_processor, soup, config
            )
        elif isinstance(element, Tag):
            self._process_tag_element(
                element, path, text_nodes, chinese_pattern,
                allowed_selectors, selector_processor, soup, config
            )


class SelectorProcessorService(ISelectorProcessor):
    """选择器处理器服务实现"""
    
    def parse_selectors(self, selector_string: str) -> List[CssSelector]:
        """解析选择器字符串"""
        if not selector_string:
            return []
        
        selectors = []
        for selector in selector_string.split(','):
            selector = selector.strip()
            if selector:
                selectors.append(CssSelector(selector=selector))
        
        return selectors
    
    def should_process_element(
        self, 
        element: Any, 
        selectors: List[CssSelector], 
        soup: BeautifulSoup
    ) -> bool:
        """判断元素是否应该被处理"""
        if not selectors:
            return True  # 如果没有限制，处理所有元素
        
        # 检查当前元素是否匹配任何选择器
        for css_selector in selectors:
            if not css_selector.is_valid:
                continue
                
            try:
                # 使用CSS选择器查找匹配的元素
                matching_elements = soup.select(css_selector.selector)
                if element in matching_elements:
                    return True
                
                # 检查元素的父级是否匹配选择器
                parent = element.parent
                while parent and parent != soup:
                    if parent in matching_elements:
                        return True
                    parent = parent.parent
                    
            except Exception as e:
                # 如果CSS选择器失败，尝试简单的标签名匹配
                if hasattr(element, 'name') and element.name == css_selector.selector:
                    return True
        
        return False


# 创建全局实例
dom_parser_service = DomParserService()
selector_processor_service = SelectorProcessorService()
