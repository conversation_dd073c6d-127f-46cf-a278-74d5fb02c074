"""
缓存工具服务实现
缓存服务中抽取的工具逻辑
"""

import hashlib
import json
import gzip
from datetime import datetime, timedelta
from typing import Dict, Any
from app.interfaces.services.cache_service_interface import (
    ICacheKeyGenerator,
    ICacheSerializer,
    ICacheCompressor
)
from app.models.entities.cache_entity import (
    CacheKey,
    CacheEntry,
    CacheMetadata,
    CacheConfig
)


class CacheKeyGenerator(ICacheKeyGenerator):
    """缓存键生成器实现"""

    def generate_key(self, cache_key: CacheKey) -> str:
        """生成缓存键"""
        # 使用标准化的标识符生成MD5哈希
        content = cache_key.cache_identifier
        path_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:10]

        # 生成简短的语言对标识
        source_short = cache_key.source_language.value
        target_short = cache_key.target_language.value[:3]

        # 格式: r:哈希:语言对
        return f"r:{path_hash}:{source_short}-{target_short}"

    def validate_key(self, cache_key: CacheKey) -> bool:
        """验证缓存键"""
        try:
            # 检查路径是否有效
            if not cache_key.path.strip():
                return False

            # 检查语言代码是否有效
            if not cache_key.source_language or not cache_key.target_language:
                return False

            return True
        except Exception:
            return False


class FileCacheKeyGenerator(CacheKeyGenerator):
    """文件缓存键生成器"""

    def generate_key(self, cache_key: CacheKey) -> str:
        """生成文件缓存键"""
        content = cache_key.cache_identifier
        return hashlib.md5(content.encode('utf-8')).hexdigest()


class CacheSerializer(ICacheSerializer):
    """缓存序列化器实现"""

    def serialize(self, entry: CacheEntry) -> bytes:
        """序列化缓存条目"""
        # 构建序列化数据
        data = {
            "metadata": {
                "cache_key": entry.metadata.cache_key,
                "created_at": entry.metadata.created_at.isoformat(),
                "expires_at": entry.metadata.expires_at.isoformat() if entry.metadata.expires_at else None,
                "source_language": entry.metadata.source_language.value,
                "target_language": entry.metadata.target_language.value,
                "path": entry.metadata.path,
                "cache_method": entry.metadata.cache_method,
                "ttl_seconds": entry.metadata.ttl_seconds
            },
            "content": entry.content
        }

        # 序列化为JSON字符串
        json_str = json.dumps(data, ensure_ascii=False)
        return json_str.encode('utf-8')

    def deserialize(self, data: bytes) -> CacheEntry:
        """反序列化缓存条目"""
        # 解码JSON字符串
        json_str = data.decode('utf-8')
        data_dict = json.loads(json_str)

        # 重建元数据
        metadata_dict = data_dict["metadata"]
        metadata = CacheMetadata(
            cache_key=metadata_dict["cache_key"],
            created_at=datetime.fromisoformat(metadata_dict["created_at"]),
            expires_at=datetime.fromisoformat(metadata_dict["expires_at"]) if metadata_dict["expires_at"] else None,
            source_language=metadata_dict["source_language"],
            target_language=metadata_dict["target_language"],
            path=metadata_dict["path"],
            cache_method=metadata_dict["cache_method"],
            ttl_seconds=metadata_dict["ttl_seconds"]
        )

        # 重建缓存条目
        return CacheEntry(
            metadata=metadata,
            content=data_dict["content"]
        )


class CacheCompressor(ICacheCompressor):
    """缓存压缩器实现"""

    def __init__(self, min_size: int = 1024):
        self.min_size = min_size

    def compress(self, data: bytes) -> bytes:
        """压缩数据"""
        return gzip.compress(data)

    def decompress(self, data: bytes) -> bytes:
        """解压数据"""
        # 检查是否为gzip格式
        if data.startswith(b'\x1f\x8b'):
            return gzip.decompress(data)
        return data

    def should_compress(self, data: bytes) -> bool:
        """是否应该压缩"""
        return len(data) >= self.min_size


class CacheMetadataBuilder:
    """缓存元数据构建器"""

    @staticmethod
    def build_metadata(
        cache_key: CacheKey,
        generated_key: str,
        config,  # 接受任何配置类型
        cache_method: str
    ) -> CacheMetadata:
        """构建缓存元数据"""
        now = datetime.now()
        expires_at = now + timedelta(seconds=config.ttl_seconds) if config.ttl_seconds > 0 else None

        return CacheMetadata(
            cache_key=generated_key,
            created_at=now,
            expires_at=expires_at,
            source_language=cache_key.source_language,
            target_language=cache_key.target_language,
            path=cache_key.path,
            cache_method=cache_method,
            ttl_seconds=config.ttl_seconds
        )


class CachePathNormalizer:
    """缓存路径标准化器"""

    @staticmethod
    def normalize_path(path: str) -> str:
        """标准化路径"""
        # 移除查询参数和锚点
        if '?' in path:
            path = path.split('?')[0]
        if '#' in path:
            path = path.split('#')[0]

        # 标准化斜杠和大小写
        path = path.lower().rstrip('/')

        return path

    @staticmethod
    def validate_path(path: str) -> bool:
        """验证路径"""
        if not path or not path.strip():
            return False

        # 检查路径长度
        if len(path) > 2048:  # URL最大长度限制
            return False

        return True


# 创建全局实例
cache_key_generator = CacheKeyGenerator()
file_cache_key_generator = FileCacheKeyGenerator()
cache_serializer = CacheSerializer()
cache_compressor = CacheCompressor()
cache_metadata_builder = CacheMetadataBuilder()
cache_path_normalizer = CachePathNormalizer()
