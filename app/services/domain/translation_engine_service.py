"""
翻译引擎领域服务
包含翻译相关的核心业务逻辑
"""

from typing import List, Dict, Any, Optional
from app.interfaces.services.translation_engine_interface import ITranslationEngine
from app.services.infrastructure.baidu_translation_engine import baidu_translation_engine


class TranslationEngineService(ITranslationEngine):
    """翻译引擎领域服务 - 核心翻译业务逻辑"""
    
    def __init__(self):
        self.engine = baidu_translation_engine
    
    async def translate_text(self, text: str, from_lang: str, to_lang: str) -> Dict[str, Any]:
        """翻译单个文本"""
        return await self.engine.translate_text(text, from_lang, to_lang)
    
    async def batch_translate(self, texts: List[str], from_lang: str, to_lang: str) -> Dict[str, Any]:
        """批量翻译文本"""
        return await self.engine.batch_translate(texts, from_lang, to_lang)
    
    async def concurrent_batch_translate(
        self,
        texts: List[str],
        from_lang: str,
        to_lang: str,
        max_concurrent: Optional[int] = None  # 如果为None，使用配置中的值
    ) -> Dict[str, Any]:
        """并发批量翻译"""
        return await self.engine.concurrent_batch_translate(texts, from_lang, to_lang, max_concurrent)
    
    def validate_language_pair(self, from_lang: str, to_lang: str) -> bool:
        """验证语言对是否支持"""
        return self.engine.validate_language_pair(from_lang, to_lang)
    
    def get_supported_languages(self) -> List[str]:
        """获取支持的语言列表"""
        return self.engine.get_supported_languages()
    
    def estimate_translation_cost(self, texts: List[str]) -> Dict[str, Any]:
        """估算翻译成本"""
        total_chars = sum(len(text) for text in texts)
        return {
            "total_texts": len(texts),
            "total_characters": total_chars,
            "estimated_requests": (len(texts) + 9) // 10,  # 每次最多10个文本
            "estimated_time_seconds": len(texts) * 0.1  # 估算时间
        }


# 创建全局实例
translation_engine_service = TranslationEngineService()
