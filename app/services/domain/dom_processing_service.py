"""
DOM处理领域服务
包含DOM解析、文本提取、文本替换等核心业务逻辑
"""

from typing import Dict, Any, Tuple
from app.interfaces.services.dom_service_interface import IDomReplacementService
from app.services.infrastructure.dom_parser_service import dom_parser_service
from app.services.infrastructure.text_extractor_service import text_extractor_service
from app.services.infrastructure.text_replacer_service import text_replacer_service
from app.services.infrastructure.text_processor_service import text_processor_service


class DomProcessingService(IDomReplacementService):
    """DOM处理领域服务 - 核心业务逻辑"""
    
    def __init__(self):
        self.dom_parser = dom_parser_service
        self.text_extractor = text_extractor_service
        self.text_replacer = text_replacer_service
        self.text_processor = text_processor_service
    
    def extract_all_chinese_with_dom(self, html_body: str, untranslatable_tags: str = None) -> Dict:
        """使用DOM解析提取所有中文文本"""
        return self.text_extractor.extract_all_chinese_with_dom(html_body, untranslatable_tags)
    
    def ultimate_replace_chinese(
        self, 
        html_body: str, 
        translation_map: Dict[str, str], 
        untranslatable_tags: str = None
    ) -> Tuple[str, Dict]:
        """终极中文替换 - 多重策略确保最高替换率"""
        return self.text_replacer.ultimate_replace_chinese(html_body, translation_map, untranslatable_tags)
    
    def create_translation_map(self, translation_results: Dict) -> Dict[str, str]:
        """创建翻译映射表"""
        return self.text_processor.create_translation_map(translation_results)
    
    def validate_html_structure(self, original_html: str, translated_html: str) -> Dict[str, Any]:
        """验证HTML结构完整性"""
        return self.dom_parser.validate_html_structure(original_html, translated_html)
    
    def extract_text_statistics(self, html_body: str) -> Dict[str, Any]:
        """提取文本统计信息"""
        return self.text_extractor.extract_text_statistics(html_body)
    
    def optimize_translation_map(self, translation_map: Dict[str, str]) -> Dict[str, str]:
        """优化翻译映射表"""
        return self.text_processor.optimize_translation_map(translation_map)


# 创建全局实例
dom_processing_service = DomProcessingService()
