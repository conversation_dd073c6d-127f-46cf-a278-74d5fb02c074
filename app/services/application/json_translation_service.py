"""
JSON翻译应用服务 - 使用方案B分段翻译法实现0瑕疵翻译
"""

import json
import re
import time
from typing import Dict, Any, List, Union
from app.services.infrastructure.baidu_translation_engine import BaiduTranslationEngine
from app.models.entities.translation_engine_entity import TranslationRequest


class JsonTranslationService:
    """JSON翻译服务 - 0瑕疵完美翻译"""
    
    def __init__(self):
        self.baidu_engine = BaiduTranslationEngine()
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
        self.total_count = 0
        self.translated_count = 0
        
    def contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文"""
        if not isinstance(text, str):
            return False
        return bool(self.chinese_pattern.search(text))
    
    async def translate_text_basic(self, text: str, from_lang: str = "zh", to_lang: str = "en") -> str:
        """基础翻译方法（不处理HTML）"""
        if not text or not isinstance(text, str) or not self.contains_chinese(text):
            return text
        
        try:
            # 创建翻译请求
            request = TranslationRequest(
                text=text,
                from_lang=from_lang,
                to_lang=to_lang
            )
            
            # 使用百度翻译引擎
            result = await self.baidu_engine.translate_single(request)
            
            if result.success and result.translated and result.translated != text:
                return result.translated
            else:
                return text
                
        except Exception as e:
            return text

    async def perfect_translate_segment(self, text: str, from_lang: str = "zh", to_lang: str = "en") -> str:
        """
        方案B：分段翻译法 - 0瑕疵HTML处理
        - 最精确的处理
        - 分别处理HTML标签和文本
        - 保持原始结构
        """
        if not text or not isinstance(text, str) or not self.contains_chinese(text):
            return text
        
        try:
            # 检查是否包含HTML标签
            if '<' not in text or '>' not in text:
                return await self.translate_text_basic(text, from_lang, to_lang)
            
            self.total_count += 1

            # 按HTML标签分割文本
            parts = re.split(r'(<[^>]*>)', text)
            
            translated_parts = []
            for i, part in enumerate(parts):
                if part.startswith('<') and part.endswith('>'):
                    # HTML标签保持不变
                    translated_parts.append(part)
                elif self.contains_chinese(part.strip()):
                    # 翻译中文文本
                    translated = await self.translate_text_basic(part.strip(), from_lang, to_lang)
                    # 保持原始的空白字符
                    if part != part.strip():
                        # 如果原文有前后空白，保持空白
                        leading_space = part[:len(part) - len(part.lstrip())]
                        trailing_space = part[len(part.rstrip()):]
                        translated_parts.append(leading_space + translated + trailing_space)
                    else:
                        translated_parts.append(translated)
                else:
                    # 其他内容保持不变
                    translated_parts.append(part)
            
            final_result = ''.join(translated_parts)
            self.translated_count += 1
            return final_result

        except Exception as e:
            return await self.translate_text_basic(text, from_lang, to_lang)

    async def translate_json_data(self, data: Dict[str, Any], target_language: str = "en") -> Dict[str, Any]:
        """翻译JSON数据"""

        # 重置计数器
        self.total_count = 0
        self.translated_count = 0
        
        # 1. 翻译系统消息
        if "msg" in data and self.contains_chinese(data["msg"]):
            data["msg"] = await self.perfect_translate_segment(data["msg"], "zh", target_language)

        # 2. 翻译搜索信息
        if "data" in data and "searchInfo" in data["data"]:
            search_info = data["data"]["searchInfo"]
            
            if "searchWord" in search_info and self.contains_chinese(search_info["searchWord"]):
                search_info["searchWord"] = await self.perfect_translate_segment(search_info["searchWord"], "zh", target_language)

            if "hitWord" in search_info and self.contains_chinese(search_info["hitWord"]):
                search_info["hitWord"] = await self.perfect_translate_segment(search_info["hitWord"], "zh", target_language)

        # 3. 翻译右侧栏
        if "data" in data and "right" in data["data"]:
            right_section = data["data"]["right"]

            # 处理 right.box 数组结构
            if "box" in right_section and isinstance(right_section["box"], list):
                for box in right_section["box"]:
                    # 翻译box的name字段
                    if "name" in box and self.contains_chinese(box["name"]):
                        box["name"] = await self.perfect_translate_segment(box["name"], "zh", target_language)

                    # 翻译box.list中的title字段
                    if "list" in box and isinstance(box["list"], list):
                        for item in box["list"]:
                            if "title" in item and self.contains_chinese(item["title"]):
                                item["title"] = await self.perfect_translate_segment(item["title"], "zh", target_language)

        # 4. 翻译文档数据
        if "data" in data and "middle" in data["data"] and "listAndBox" in data["data"]["middle"]:
            list_and_box = data["data"]["middle"]["listAndBox"]
            
            for idx, doc_item in enumerate(list_and_box, 1):
                if "data" not in doc_item:
                    continue
                    
                doc_data = doc_item["data"]
                
                # 翻译各个字段
                fields_to_translate = ["table-2", "table-3", "table-5", "table-6", "table-7", 
                                     "source", "title", "title_no_tag"]
                
                for field in fields_to_translate:
                    if field in doc_data and self.contains_chinese(doc_data[field]):
                        doc_data[field] = await self.perfect_translate_segment(doc_data[field], "zh", target_language)
                
                # 翻译table-8字段（包含嵌套JSON）
                if "table-8" in doc_data and doc_data["table-8"]:
                    try:
                        # 解析嵌套的JSON字符串
                        table8_data = json.loads(doc_data["table-8"])
                        if "DATA" in table8_data and isinstance(table8_data["DATA"], list):
                            for item in table8_data["DATA"]:
                                if "TITLE" in item and self.contains_chinese(item["TITLE"]):
                                    item["TITLE"] = await self.perfect_translate_segment(item["TITLE"], "zh", target_language)
                            
                            # 重新序列化为JSON字符串
                            doc_data["table-8"] = json.dumps(table8_data, ensure_ascii=False)
                    except (json.JSONDecodeError, Exception) as e:
                        pass  # 忽略JSON解析错误

                # 翻译table-11标签数组
                if "table-11" in doc_data and isinstance(doc_data["table-11"], list):
                    for tag_idx, tag in enumerate(doc_data["table-11"]):
                        if self.contains_chinese(tag):
                            doc_data["table-11"][tag_idx] = await self.perfect_translate_segment(tag, "zh", target_language)

        return data

    def verify_translation(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证翻译结果"""
        chinese_count = 0
        total_fields = 0
        
        def count_chinese_recursive(obj, path=""):
            nonlocal chinese_count, total_fields
            
            if isinstance(obj, dict):
                for key, value in obj.items():
                    count_chinese_recursive(value, f"{path}.{key}" if path else key)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    count_chinese_recursive(item, f"{path}[{i}]")
            elif isinstance(obj, str):
                total_fields += 1
                if self.contains_chinese(obj):
                    chinese_count += 1
        
        count_chinese_recursive(data)
        
        return {
            "total_fields": total_fields,
            "chinese_fields_remaining": chinese_count,
            "translated_fields": self.translated_count,
            "success_rate": ((self.translated_count / max(chinese_count + self.translated_count, 1)) * 100) if (chinese_count + self.translated_count) > 0 else 100
        }

    async def translate_json(self, json_data: Dict[str, Any], target_language: str) -> Dict[str, Any]:
        """主要的JSON翻译方法"""
        start_time = time.time()
        
        try:
            
            # 翻译JSON数据
            translated_data = await self.translate_json_data(json_data, target_language)
            
            # 验证翻译结果
            stats = self.verify_translation(translated_data)
            stats["processing_time"] = time.time() - start_time
            
            pass
            
            return {
                "success": True,
                "message": "JSON翻译完成" if stats['chinese_fields_remaining'] == 0 else f"JSON翻译完成，还有{stats['chinese_fields_remaining']}个中文字段未翻译",
                "translated_data": translated_data,
                "statistics": stats
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                "success": False,
                "message": "JSON翻译失败",
                "error": str(e),
                "statistics": {
                    "processing_time": processing_time
                }
            }


# 创建全局服务实例
json_translation_service = JsonTranslationService()
