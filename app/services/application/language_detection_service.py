"""
语言识别翻译应用服务 - 应用服务层
负责语言识别和翻译到中文的业务流程编排
"""

from typing import Dict, Any
from app.services.infrastructure.qwen_mt_translation_engine import qwen_mt_translation_engine


class LanguageDetectionService:
    """语言识别翻译应用服务"""

    def __init__(self):
        self.translation_engine = qwen_mt_translation_engine

    async def detect_and_translate_to_chinese(self, text: str) -> Dict[str, Any]:
        """
        检测语言并翻译成中文

        Args:
            text: 输入的文本

        Returns:
            包含检测语言、翻译结果的字典
        """
        try:
            # 1. 使用Qwen3-MT检测语言
            detected_language = await self._detect_language(text)

            # 2. 如果已经是中文，直接返回
            if detected_language == "zh":
                return {
                    "success": True,
                    "original_text": text,
                    "detected_language": "zh",
                    "detected_language_name": "中文",
                    "translated_text": text,
                    "message": "输入已是中文，无需翻译"
                }

            # 3. 翻译成中文
            translation_result = await self._translate_to_chinese(text, detected_language)

            return {
                "success": True,
                "original_text": text,
                "detected_language": detected_language,
                "detected_language_name": self._get_language_name(detected_language),
                "translated_text": translation_result,
                "message": f"从{self._get_language_name(detected_language)}翻译成中文"
            }

        except Exception as e:
            error_msg = str(e) if hasattr(e, '__str__') else repr(e)
            return {
                "success": False,
                "original_text": text,
                "detected_language": None,
                "detected_language_name": None,
                "translated_text": None,
                "error": f"语言识别翻译失败: {error_msg}"
            }

    async def _detect_language(self, text: str) -> str:
        """检测文本语言"""
        # 直接使用字符特征检测语言
        return self._simple_language_detection(text)

    def _simple_language_detection(self, text: str) -> str:
        """简单的字符特征语言检测"""
        text_lower = text.lower()
        
        # 中文字符
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return "zh"

        # 泰语字符
        if any('\u0e00' <= char <= '\u0e7f' for char in text):
            return "th"

        # 越南语特殊字符（包含声调符号）
        vietnamese_chars = 'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ'
        vietnamese_words = ['xin', 'chào', 'bạn', 'có', 'khỏe', 'không', 'tôi', 'là', 'của', 'và', 'với', 'trong']
        
        # 检查越南语特殊字符
        if any(char in vietnamese_chars for char in text_lower):
            return "vie"
        
        # 检查越南语常用词汇
        if any(word in text_lower for word in vietnamese_words):
            return "vie"

        # 高棉语字符
        if any('\u1780' <= char <= '\u17ff' for char in text):
            return "hkm"

        # 老挝语字符
        if any('\u0e80' <= char <= '\u0eff' for char in text):
            return "lao"

        # 缅甸语字符
        if any('\u1000' <= char <= '\u109f' for char in text):
            return "bur"

        # 泰米尔语字符
        if any('\u0b80' <= char <= '\u0bff' for char in text):
            return "tam"

        # 印尼语/马来语常用词汇检测
        indonesian_words = ['saya', 'anda', 'dengan', 'untuk', 'dari', 'ini', 'itu', 'yang', 'adalah', 'tidak']
        malay_words = ['saya', 'awak', 'dengan', 'untuk', 'daripada', 'ini', 'itu', 'yang', 'adalah', 'tidak']
        
        if any(word in text_lower for word in indonesian_words):
            return "id"
        if any(word in text_lower for word in malay_words):
            return "may"

        # 菲律宾语常用词汇
        filipino_words = ['ako', 'ikaw', 'siya', 'tayo', 'kayo', 'sila', 'ang', 'ng', 'sa', 'ay']
        if any(word in text_lower for word in filipino_words):
            return "fil"

        # 默认返回英语
        return "en"

    async def _translate_to_chinese(self, text: str, from_lang: str) -> str:
        """翻译到中文"""
        result = await self.translation_engine.translate_text(text, from_lang, "zh")

        if result.get("success"):
            return result["translated_text"]
        else:
            error_info = result.get('error', '未知错误')
            raise Exception(f"翻译失败: {error_info}")

    def _get_language_name(self, lang_code: str) -> str:
        """获取语言名称"""
        language_names = {
            "zh": "中文",
            "en": "英语",
            "may": "马来语",
            "hkm": "高棉语",
            "id": "印尼语",
            "bur": "缅甸语",
            "fil": "菲律宾语",
            "th": "泰语",
            "vie": "越南语",
            "tam": "泰米尔语",
            "lao": "老挝语"
        }
        return language_names.get(lang_code, f"未知语言({lang_code})")


# 创建全局实例
language_detection_service = LanguageDetectionService()
