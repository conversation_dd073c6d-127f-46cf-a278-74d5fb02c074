"""
图片 OCR 应用服务
"""

import time
import re
import base64
import io
import aiohttp
import asyncio
from typing import List, Dict, Any
from PIL import Image
from app.config.config import get_settings

from app.dto.requests.image_ocr_request_dto import ImageOCRRequestDTO
from app.dto.responses.image_ocr_response_dto import ImageOCRResponseDTO
from app.dto.requests.batch_image_ocr_request_dto import BatchImageOCRRequestDTO
from app.dto.responses.batch_image_ocr_response_dto import BatchImageOCRResponseDTO, ImageOCRResult
from app.services.infrastructure.baidu_translation_engine import baidu_translation_engine
from app.services.infrastructure.image_cache_service import image_cache_service
from app.models.entities.translation_engine_entity import TranslationRequest


class ImageOCRService:
    """图片 OCR 应用服务"""

    def __init__(self):
        """初始化服务"""
        settings = get_settings()
        self.api_key = settings.deepseek_api_key
        self.api_url = settings.deepseek_api_url
        self.text_model = settings.deepseek_model  # 文本翻译模型
        self.vision_model = settings.deepseek_vision_model  # 视觉识别模型
        self.timeout = settings.deepseek_timeout
        self.max_tokens = settings.deepseek_max_tokens
        self.temperature = settings.deepseek_temperature

        # 图片批量处理配置
        self.max_concurrent = settings.image_batch_max_concurrent

    def base64_to_image(self, base64_str: str) -> Image.Image:
        """将 Base64 字符串转换为 PIL Image 对象"""
        try:
            # 如果包含 data:image 前缀，去掉它
            if base64_str.startswith('data:image'):
                base64_str = base64_str.split(',')[1]

            # 解码 Base64
            image_data = base64.b64decode(base64_str)

            # 转换为 PIL Image
            image = Image.open(io.BytesIO(image_data))
            return image
        except Exception:
            return None



    async def extract_text_from_base64(self, base64_str: str) -> List[str]:
        """使用DeepSeek视觉模型从 Base64 图片中提取文本"""
        try:
            result = await self._extract_with_deepseek(base64_str)
            return result
        except Exception:
            return []

    def _convert_image_to_supported_format(self, base64_str: str) -> str:
        """将前端传来的base64图片转换为DeepSeek支持的格式"""
        try:
            # 处理base64字符串，去除data:image前缀
            clean_base64 = base64_str
            if base64_str.startswith('data:image'):
                clean_base64 = base64_str.split(',')[1]

            # 解码图片数据
            image_data = base64.b64decode(clean_base64)
            image = Image.open(io.BytesIO(image_data))

            # 转换为RGB模式（DeepSeek要求）
            if image.mode in ('RGBA', 'LA', 'P'):
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                # 处理透明通道
                if image.mode in ('RGBA', 'LA'):
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image)
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # 保存为JPEG格式（DeepSeek支持的格式）
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=95, optimize=True)
            output.seek(0)

            # 转换为base64
            converted_base64 = base64.b64encode(output.getvalue()).decode('utf-8')
            return converted_base64

        except Exception:
            # 如果转换失败，返回原始base64（去除前缀）
            if base64_str.startswith('data:image'):
                return base64_str.split(',')[1]
            return base64_str

    async def _extract_with_deepseek(self, base64_str: str) -> List[str]:
        """使用DeepSeek视觉模型进行图像文字识别"""
        try:
            # 转换图片格式为DeepSeek支持的格式
            converted_base64 = self._convert_image_to_supported_format(base64_str)

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # 构建请求数据
            data = {
                "model": self.vision_model,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "请识别这张图片中的所有文字，包括中文、英文、数字等。请将识别出的文字逐行列出，每行一个完整的文字内容。只返回识别出的文字，不要添加序号、解释或其他格式。"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{converted_base64}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": self.max_tokens,
                "temperature": self.temperature
            }

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(self.api_url, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()

                        if "choices" in result and len(result["choices"]) > 0:
                            content = result["choices"][0]["message"]["content"].strip()
                            texts = self._parse_deepseek_result(content)
                            return texts
                        else:
                            return []
                    else:
                        return []

        except asyncio.TimeoutError:
            return []
        except Exception:
            return []

    def _parse_deepseek_result(self, content: str) -> List[str]:
        """解析DeepSeek返回的识别结果"""
        try:
            # 按行分割
            lines = content.strip().split('\n')
            texts = []

            for line in lines:
                line = line.strip()

                # 移除可能的序号、符号等
                line = re.sub(r'^[\d\.\-\*\+\s]*', '', line)
                line = line.strip('\"\'')

                # 保留所有有意义的文字
                if line and len(line.strip()) > 0:
                    if re.search(r'[\u4e00-\u9fff\w]', line):
                        texts.append(line)

            return texts

        except Exception:
            return [content] if content else []

    def filter_chinese_text(self, texts: List[str]) -> List[str]:
        """过滤出中文文字"""
        chinese_texts = []
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')

        for text in texts:
            # 提取文本中的中文字符
            chinese_matches = chinese_pattern.findall(text)
            if chinese_matches:
                # 合并所有中文字符
                chinese_text = ''.join(chinese_matches)
                if chinese_text.strip():
                    chinese_texts.append(chinese_text)

        return chinese_texts

    async def translate_chinese_texts(self, chinese_texts: List[str], target_language: str) -> List[str]:
        """翻译中文文字"""
        if not chinese_texts:
            return []

        translated_texts = []

        for text in chinese_texts:
            try:
                # 创建翻译请求
                request = TranslationRequest(
                    text=text,
                    from_lang="zh",
                    to_lang=target_language
                )

                # 调用百度翻译引擎
                result = await baidu_translation_engine.translate_single(request)

                if result.success and result.translated:
                    translated_texts.append(result.translated)
                else:
                    # 翻译失败，保留原文
                    translated_texts.append(text)

            except Exception:
                # 翻译失败，保留原文
                translated_texts.append(text)

        return translated_texts
    
    async def process_image_ocr(self, request: ImageOCRRequestDTO) -> ImageOCRResponseDTO:
        """处理图片 OCR 请求"""
        start_time = time.time()

        try:
            # 1. 使用DeepSeek提取图片中的文字
            all_recognized_texts = await self.extract_text_from_base64(request.base64_image)

            # 2. 过滤出中文文字
            chinese_texts = self.filter_chinese_text(all_recognized_texts)

            # 3. 翻译中文文字
            translated_texts = await self.translate_chinese_texts(chinese_texts, request.target_language)

            # 4. 计算处理时间
            processing_time = time.time() - start_time

            # 5. 构建响应
            response = ImageOCRResponseDTO(
                success=True,
                message="图片识别和翻译完成",
                base64_image=request.base64_image,  # 返回原图

                # 详细的识别和翻译结果
                ocr_raw_texts=all_recognized_texts,  # 识别的所有原始文字
                chinese_texts=chinese_texts,         # 过滤出的中文文字
                translated_texts=translated_texts,   # 翻译后的文字

                # 兼容旧字段
                recognized_texts=translated_texts,   # 兼容字段，返回翻译结果
                text_count=len(translated_texts),
                processing_time=processing_time
            )

            return response

        except Exception as e:
            processing_time = time.time() - start_time

            return ImageOCRResponseDTO(
                success=False,
                message="图片识别和翻译失败",
                base64_image=None,
                recognized_texts=[],
                text_count=0,
                processing_time=processing_time,
                error=str(e)
            )

    async def process_single_image_with_cache(self, base64_image: str, target_language: str, index: int) -> ImageOCRResult:
        """处理单张图片（带缓存）"""
        start_time = time.time()

        try:
            # 1. 检查缓存
            cached_result = image_cache_service.get_cached_result(base64_image, target_language)
            if cached_result:
                processing_time = time.time() - start_time
                return ImageOCRResult(
                    index=index,
                    success=True,
                    message="图片识别和翻译完成（来自缓存）",
                    base64_image=base64_image,
                    ocr_raw_texts=cached_result.get('ocr_raw_texts', []),
                    chinese_texts=cached_result.get('chinese_texts', []),
                    translated_texts=cached_result.get('translated_texts', []),
                    recognized_texts=cached_result.get('translated_texts', []),
                    text_count=len(cached_result.get('translated_texts', [])),
                    processing_time=processing_time,
                    from_cache=True,
                    cache_key=cached_result.get('cache_key', '')
                )

            # 2. 实际处理
            all_recognized_texts = await self.extract_text_from_base64(base64_image)
            chinese_texts = self.filter_chinese_text(all_recognized_texts)
            translated_texts = await self.translate_chinese_texts(chinese_texts, target_language)

            processing_time = time.time() - start_time

            # 3. 构建结果
            result_data = {
                'ocr_raw_texts': all_recognized_texts,
                'chinese_texts': chinese_texts,
                'translated_texts': translated_texts,
                'text_count': len(translated_texts)
            }

            # 4. 保存到缓存
            cache_key = image_cache_service.save_cache_result(base64_image, target_language, result_data)

            return ImageOCRResult(
                index=index,
                success=True,
                message="图片识别和翻译完成",
                base64_image=base64_image,
                ocr_raw_texts=all_recognized_texts,
                chinese_texts=chinese_texts,
                translated_texts=translated_texts,
                recognized_texts=translated_texts,
                text_count=len(translated_texts),
                processing_time=processing_time,
                from_cache=False,
                cache_key=cache_key
            )

        except Exception as e:
            processing_time = time.time() - start_time
            return ImageOCRResult(
                index=index,
                success=False,
                message="图片识别和翻译失败",
                base64_image=base64_image,
                processing_time=processing_time,
                error=str(e)
            )


    async def process_batch_image_ocr(self, request: BatchImageOCRRequestDTO) -> BatchImageOCRResponseDTO:
        """处理批量图片 OCR 请求"""
        start_time = time.time()

        try:
            # 1. 创建并发任务
            semaphore = asyncio.Semaphore(self.max_concurrent)

            async def process_with_semaphore(image_data: str, index: int):
                async with semaphore:
                    return await self.process_single_image_with_cache(
                        image_data,
                        request.target_language,
                        index
                    )

            # 2. 并发处理所有图片
            tasks = [
                process_with_semaphore(image_data, index)
                for index, image_data in enumerate(request.images)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 3. 处理结果
            processed_results = []
            errors = []

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # 处理异常
                    error_result = ImageOCRResult(
                        index=i,
                        success=False,
                        message="图片处理异常",
                        base64_image=request.images[i] if i < len(request.images) else "",
                        error=str(result)
                    )
                    processed_results.append(error_result)
                    errors.append(f"图片{i+1}: {str(result)}")
                else:
                    processed_results.append(result)

            # 4. 统计信息
            total_processing_time = time.time() - start_time
            successful_images = sum(1 for r in processed_results if r.success)
            failed_images = len(processed_results) - successful_images
            cached_images = sum(1 for r in processed_results if r.from_cache)

            average_processing_time = 0
            if successful_images > 0:
                total_individual_time = sum(r.processing_time or 0 for r in processed_results if r.success)
                average_processing_time = total_individual_time / successful_images

            # 5. 构建响应
            response = BatchImageOCRResponseDTO(
                success=failed_images == 0,
                message=f"批量处理完成：成功{successful_images}张，失败{failed_images}张",
                results=processed_results,
                total_images=len(request.images),
                successful_images=successful_images,
                failed_images=failed_images,
                cached_images=cached_images,
                total_processing_time=total_processing_time,
                average_processing_time=average_processing_time,
                max_concurrent=self.max_concurrent,
                cache_enabled=True,
                cache_ttl_minutes=-1,  # 显示为永久缓存（实际按清理间隔删除）
                errors=errors
            )

            return response

        except Exception as e:
            total_processing_time = time.time() - start_time

            return BatchImageOCRResponseDTO(
                success=False,
                message=f"批量处理失败: {str(e)}",
                results=[],
                total_images=len(request.images),
                successful_images=0,
                failed_images=len(request.images),
                cached_images=0,
                total_processing_time=total_processing_time,
                max_concurrent=self.max_concurrent,
                cache_enabled=True,
                cache_ttl_minutes=-1,  # 显示为永久缓存（实际按清理间隔删除）
                errors=[str(e)]
            )


# 创建服务实例
image_ocr_service = ImageOCRService()
