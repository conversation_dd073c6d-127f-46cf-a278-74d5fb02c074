"""
翻译应用服务
负责翻译业务流程的编排和协调
"""

from typing import Dict, Any, Optional
from app.interfaces.services.translation_service_interface import ITranslationService
from app.models.entities.translation_entity import TranslationTask
from app.models.enums.translation_enums import ProcessingMode, CacheStrategy, HtmlSizeCategory
from app.services.adapters.translation_engine_adapter import translation_engine_adapter
from app.services.adapters.dom_replacement_service_adapter import dom_replacement_service_adapter
from app.services.adapters.cache_service_adapter import (
    file_cache_service_adapter,
    redis_cache_service_adapter
)
from app.services.infrastructure.content_hash_cache_service import content_hash_cache_service
from app.services.infrastructure.sensitive_word_processor import sensitive_word_processor
from app.services.infrastructure.date_processor import date_processor
from app.services.infrastructure.city_processor import city_processor
from app.config.config import get_settings



class TranslationApplicationService(ITranslationService):
    """翻译应用服务 - 负责业务流程编排"""

    def __init__(self):
        self.translation_engine = translation_engine_adapter
        self.dom_service = dom_replacement_service_adapter
        self.file_cache = file_cache_service_adapter
        self.redis_cache = redis_cache_service_adapter
        self.content_hash_cache = content_hash_cache_service
        self.settings = get_settings()

    async def translate_ultimate(self, task: TranslationTask) -> Dict[str, Any]:
        """终极翻译 - 100%替换率"""
        # 1. 检查服务可用性
        if not self.translation_engine:
            raise Exception("翻译服务初始化失败，检查.env配置")

        # 2. 敏感词预处理（必须在缓存检查之前）
        target_lang_code = task.target_language.value  # 转换枚举为字符串
        sensitive_result = sensitive_word_processor.process_text(task.html_body, target_lang_code)

        if sensitive_result.has_sensitive_words:
            # 更新任务的HTML内容为预处理后的内容
            task.html_body = sensitive_result.processed_text

        # 3. 日期预处理（在敏感词处理之后）
        date_result = date_processor.process_text(task.html_body, target_lang_code)

        if date_result.has_dates:
            # 更新任务的HTML内容为日期预处理后的内容
            task.html_body = date_result.processed_text

        # 4. 城市名预处理（在日期处理之后）
        city_result = city_processor.process_text(task.html_body, target_lang_code)

        if city_result.has_cities:
            # 更新任务的HTML内容为城市预处理后的内容
            task.html_body = city_result.processed_text

        # 4. 检查统一缓存（使用预处理后的内容）
        cached_result = await self.get_cached_result_with_content_hash(task, "auto", False)
        if cached_result:
            return {
                "success": True,
                "message": "使用缓存结果！",
                "data": cached_result
            }

        # 4. 根据HTML大小选择处理模式
        html_category = HtmlSizeCategory.categorize(task.html_length)

        if html_category == HtmlSizeCategory.LARGE:
            return await self._process_large_html(task)
        else:
            return await self._process_standard_html(task)

    async def translate_safe(self, task: TranslationTask) -> Dict[str, Any]:
        """安全翻译 - 0%破坏率"""
        # 1. 敏感词预处理（必须在缓存检查之前）
        target_lang_code = task.target_language.value  # 转换枚举为字符串
        sensitive_result = sensitive_word_processor.process_text(task.html_body, target_lang_code)

        if sensitive_result.has_sensitive_words:
            # 更新任务的HTML内容为预处理后的内容
            task.html_body = sensitive_result.processed_text

        # 2. 日期预处理（在敏感词处理之后）
        date_result = date_processor.process_text(task.html_body, target_lang_code)

        if date_result.has_dates:
            # 更新任务的HTML内容为日期预处理后的内容
            task.html_body = date_result.processed_text

        # 3. 城市名预处理（在日期处理之后）
        city_result = city_processor.process_text(task.html_body, target_lang_code)

        if city_result.has_cities:
            # 更新任务的HTML内容为城市预处理后的内容
            task.html_body = city_result.processed_text

        # 4. 检查统一缓存（使用预处理后的内容）
        cached_result = await self.get_cached_result_with_content_hash(task, "auto", False)
        if cached_result:
            return {
                "success": True,
                "message": "使用缓存结果！",
                "data": cached_result
            }

        # 5. DOM解析提取中文
        dom_data = self.dom_service.extract_all_chinese_with_dom(
            task.html_body,
            task.untranslatable_tags
        )

        all_chinese_texts = dom_data['chinese_texts']
        unique_chinese_texts = list(set(all_chinese_texts))

        # 6. 检查是否有中文文本
        if not unique_chinese_texts:
            return self._create_no_translation_response(task)

        # 7. 并发翻译
        translation_results = await self.translation_engine.concurrent_batch_translate(
            unique_chinese_texts,
            task.source_language.value,
            task.target_language.value,
            None  # 使用配置中的默认并发数
        )

        # 5. 创建翻译映射
        translation_map = self.dom_service.create_translation_map(translation_results)

        # 6. 超安全替换
        translated_html_body, ultra_safe_stats = self.dom_service.ultimate_replace_chinese(
            task.html_body,
            translation_map,
            task.untranslatable_tags
        )

        # 7. 构建响应数据
        response_data = self._build_safe_translation_response(
            task, dom_data, translation_results, translated_html_body, ultra_safe_stats, translation_map
        )

        # 8. 保存到统一缓存
        await self.save_translation_result_with_content_hash(task, response_data, "auto", 3600)

        return {
            "success": True,
            "message": f"安全翻译完成！替换率: {ultra_safe_stats['replacement_rate']:.2f}%",
            "data": response_data
        }

    async def get_cached_result(self, task: TranslationTask) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        if task.cache_strategy == CacheStrategy.FILE_CACHE:
            return await self.file_cache.get_cache(
                task.path,
                task.source_language.value,
                task.target_language.value
            )
        else:
            return await self.redis_cache.get_cache(
                task.path,
                task.source_language.value,
                task.target_language.value
            )

    async def get_cached_result_with_content_hash(self, task: TranslationTask,
                                                 cache_algorithm: str = "auto",
                                                 force_refresh: bool = False) -> Optional[Dict[str, Any]]:
        """基于内容哈希获取缓存结果"""
        if force_refresh:
            return None

        # 使用task中的缓存存储策略
        use_redis = task.cache_strategy == CacheStrategy.REDIS_CACHE

        return await self.content_hash_cache.get_cache_by_content(
            path=task.path,
            content=task.html_body,
            source_lang=task.source_language.value,
            target_lang=task.target_language.value,
            cache_strategy=cache_algorithm,  # 这里是缓存算法
            use_redis=use_redis              # 这里是缓存存储
        )

    async def save_translation_result(self, task: TranslationTask, result: Dict[str, Any]) -> bool:
        """保存翻译结果到缓存"""
        if task.cache_strategy == CacheStrategy.FILE_CACHE:
            return await self.file_cache.set_cache(
                task.path,
                task.source_language.value,
                task.target_language.value,
                result
            )
        else:
            return await self.redis_cache.set_cache(
                task.path,
                task.source_language.value,
                task.target_language.value,
                result
            )

    async def save_translation_result_with_content_hash(self, task: TranslationTask,
                                                       result: Dict[str, Any],
                                                       cache_algorithm: str = "auto",
                                                       ttl_seconds: int = 3600) -> bool:
        """基于内容哈希保存翻译结果到缓存"""
        # 使用task中的缓存存储策略
        use_redis = task.cache_strategy == CacheStrategy.REDIS_CACHE

        return await self.content_hash_cache.set_cache_by_content(
            path=task.path,
            content=task.html_body,
            source_lang=task.source_language.value,
            target_lang=task.target_language.value,
            data=result,
            cache_strategy=cache_algorithm,  # 这里是缓存算法
            ttl_seconds=ttl_seconds,
            use_redis=use_redis              # 这里是缓存存储
        )

    def _create_cached_response(self, task: TranslationTask, cached_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建缓存响应"""
        cache_type = "文件" if task.cache_strategy == CacheStrategy.FILE_CACHE else "Redis"
        return {
            "success": True,
            "message": f"使用{cache_type}缓存结果！",
            "data": cached_data
        }

    def _create_no_translation_response(self, task: TranslationTask) -> Dict[str, Any]:
        """创建无需翻译的响应"""
        return {
            "success": True,
            "message": "未发现需要翻译的中文文本",
            "data": {
                "ultra_safe_replacement_results": {
                    "original_html_body": task.html_body,
                    "translated_html_body": task.html_body,
                    "replacement_statistics": {
                        "extraction_rate": 100.0,
                        "replacement_rate": 100.0,
                        "damage_rate": 0.0,
                        "original_chinese_count": 0,
                        "replaced_count": 0,
                        "final_chinese_count": 0
                    }
                }
            }
        }

    async def _process_large_html(self, task: TranslationTask) -> Dict[str, Any]:
        """处理大型HTML（使用标准流程）"""
        print("检测到大型HTML，使用优化处理流程...")
        return await self._process_standard_html(task)

    async def _process_standard_html(self, task: TranslationTask) -> Dict[str, Any]:
        """处理标准HTML"""
        # 1. DOM解析提取中文文本
        dom_data = self.dom_service.extract_all_chinese_with_dom(
            task.html_body,
            task.untranslatable_tags
        )

        # 2. 高速并发翻译
        all_chinese_texts = dom_data['chinese_texts']

        translation_results = await self.translation_engine.concurrent_batch_translate(
            all_chinese_texts,
            task.source_language.value,
            task.target_language.value,
            None  # 使用配置中的默认并发数
        )

        # 3. 创建翻译映射表
        translation_map = self.dom_service.create_translation_map(translation_results)

        # 4. 终极替换
        translated_html_body, ultimate_stats = self.dom_service.ultimate_replace_chinese(
            task.html_body,
            translation_map,
            task.untranslatable_tags
        )

        # 5. 构建响应数据
        response_data = self._build_ultimate_translation_response(
            task, dom_data, translation_results, translated_html_body, ultimate_stats, translation_map
        )

        # 6. 保存到统一缓存
        await self.save_translation_result_with_content_hash(task, response_data, "auto", 3600)

        return {
            "success": True,
            "message": f"终极翻译完成！替换率: {ultimate_stats['replacement_rate']:.2f}%",
            "data": response_data
        }

    def _build_request_info(self, task: TranslationTask, processing_mode: ProcessingMode) -> Dict[str, Any]:
        """构建请求信息"""
        return {
            "path": task.path,
            "html_length": task.html_length,
            "source_language": task.source_language.value,
            "target_language": task.target_language.value,
            "processing_mode": processing_mode.value,
            "untranslatable_tags": task.untranslatable_tags,
            "no_translate_tags": task.no_translate_tags,
            "cache_strategy": task.cache_strategy.value
        }

    def _build_ultimate_translation_response(
        self,
        task: TranslationTask,
        dom_data: Dict[str, Any],
        translation_results: Dict[str, Any],
        translated_html_body: str,
        ultimate_stats: Dict[str, Any],
        translation_map: Dict[str, str]
    ) -> Dict[str, Any]:
        """构建终极翻译响应数据"""
        return {
            "request_info": self._build_request_info(task, ProcessingMode.STANDARD),
            "dom_extraction_results": dom_data['statistics'],
            "translation_results": translation_results,
            "ultimate_replacement_results": {
                "original_html_body": task.html_body,
                "translated_html_body": translated_html_body,
                "replacement_statistics": ultimate_stats,
                "translation_map": translation_map
            }
        }

    def _build_safe_translation_response(
        self,
        task: TranslationTask,
        dom_data: Dict[str, Any],
        translation_results: Dict[str, Any],
        translated_html_body: str,
        ultra_safe_stats: Dict[str, Any],
        translation_map: Dict[str, str]
    ) -> Dict[str, Any]:
        """构建安全翻译响应数据"""
        return {
            "request_info": self._build_request_info(task, ProcessingMode.ULTRA_SAFE),
            "dom_extraction_results": dom_data['statistics'],
            "translation_results": translation_results,
            "ultra_safe_replacement_results": {
                "original_html_body": task.html_body,
                "translated_html_body": translated_html_body,
                "replacement_statistics": ultra_safe_stats,
                "translation_map": translation_map
            }
        }


# 创建全局实例
translation_application_service = TranslationApplicationService()
