"""
百度翻译API适配器
从原有服务中抽取的API调用逻辑
"""

import hashlib
import random
import aiohttp
from typing import Dict, Any
from app.interfaces.services.translation_engine_interface import ISignatureGenerator, IApiClient


class BaiduSignatureGenerator(ISignatureGenerator):
    """百度API签名生成器"""
    
    def generate_sign(self, query: str, salt: str, app_id: str, secret_key: str) -> str:
        """生成百度API签名"""
        sign_str = f"{app_id}{query}{salt}{secret_key}"
        return hashlib.md5(sign_str.encode('utf-8')).hexdigest()


class BaiduApiClient(IApiClient):
    """百度API客户端"""
    
    def __init__(self, api_url: str):
        self.api_url = api_url
    
    async def call_api(self, params: Dict[str, str], timeout: float) -> Dict[str, Any]:
        """调用百度翻译API"""
        try:
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.get(self.api_url, params=params) as response:
                    result = await response.json()
                    return self._process_api_response(result, params['q'])
        except Exception as e:
            return {
                "success": False,
                "error": f"API调用失败: {str(e)}",
                "original": params['q']
            }
    
    def _process_api_response(self, result: Dict[str, Any], original_text: str) -> Dict[str, Any]:
        """处理API响应"""
        if 'trans_result' in result:
            translated_text = result['trans_result'][0]['dst']
            return {
                "success": True,
                "original": original_text,
                "translated": translated_text
            }
        else:
            error_code = result.get('error_code', 'unknown')
            error_msg = result.get('error_msg', '未知错误')
            return {
                "success": False,
                "error": f"百度翻译API错误: {error_code} - {error_msg}",
                "original": original_text
            }


class BaiduApiRequestBuilder:
    """百度API请求构建器"""
    
    def __init__(self, signature_generator: ISignatureGenerator):
        self.signature_generator = signature_generator
    
    def build_request_params(
        self, 
        text: str, 
        from_lang: str, 
        to_lang: str, 
        app_id: str, 
        secret_key: str
    ) -> Dict[str, str]:
        """构建请求参数"""
        salt = str(random.randint(32768, 65536))
        sign = self.signature_generator.generate_sign(text, salt, app_id, secret_key)
        
        return {
            'q': text,
            'from': from_lang,
            'to': to_lang,
            'appid': app_id,
            'salt': salt,
            'sign': sign
        }
