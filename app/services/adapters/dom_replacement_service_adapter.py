"""
DOM替换服务实现 -
将原有的DOM替换服务按层次重构
"""

import time
import re
from typing import Dict, Tu<PERSON>, List
from app.interfaces.services.dom_service_interface import IDomReplacementService
from app.models.entities.dom_entity import (
    DomProcessingConfig,
    ReplacementResult,
    ReplacementStatistics
)
from app.services.infrastructure.text_extractor_service import text_extractor_service
from app.services.infrastructure.text_replacer_service import text_replacer_service


class DomReplacementServiceV2(IDomReplacementService):
    """DOM替换服务实现 - 重构版本"""

    def __init__(self):
        self.text_extractor = text_extractor_service
        self.text_replacer = text_replacer_service
        self.chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')

    def extract_all_chinese_with_dom(self, html_body: str, untranslatable_tags: str = None) -> Dict:
        """提取所有中文文本"""
        # 创建处理配置
        config = DomProcessingConfig(
            untranslatable_tags=untranslatable_tags,
            process_attributes=True,
            process_comments=False,
            process_scripts=False,
            process_styles=True
        )

        # 提取中文文本
        extraction_data = self.text_extractor.extract_chinese_texts(html_body, config)

        # 转换为原有格式（保持向后兼容）
        return {
            'soup': extraction_data.soup,
            'text_nodes': [self._convert_text_node_to_dict(node) for node in extraction_data.text_nodes],
            'chinese_texts': extraction_data.chinese_texts,
            'statistics': extraction_data.statistics
        }

    def ultimate_replace_chinese(
        self,
        html_body: str,
        translation_map: Dict[str, str],
        untranslatable_tags: str = None
    ) -> Tuple[str, Dict]:
        """终极替换中文 - 追求100%替换率"""
        start_time = time.time()

        # 创建处理配置
        config = DomProcessingConfig(
            untranslatable_tags=untranslatable_tags,
            process_attributes=True,
            process_comments=False,
            process_scripts=True,
            process_styles=True
        )

        # 统计原始中文数量
        original_chinese_count = len(self.chinese_pattern.findall(html_body))

        # 创建替换统计
        stats = ReplacementStatistics()

        # 第一步：DOM精确提取和替换
        extraction_data = self.text_extractor.extract_chinese_texts(html_body, config)
        html_after_dom = self.text_replacer.replace_in_dom(extraction_data, translation_map)
        stats.add_dom_replacement(self._count_replacements(html_body, html_after_dom))

        # 第二步：处理特殊情况
        html_after_special, special_count = self.text_replacer.replace_special_cases(
            html_after_dom, translation_map
        )
        stats.add_special_replacement(special_count)

        # 第三步：最后的暴力替换
        html_final, brute_count = self.text_replacer.brute_force_replace(
            html_after_special, translation_map, config
        )
        stats.add_brute_force_replacement(brute_count)

        # 统计最终结果
        final_chinese_count = len(self.chinese_pattern.findall(html_final))
        processing_time = round(time.time() - start_time, 2)
        stats.processing_time = processing_time

        # 计算替换率
        replacement_rate = 0.0
        if original_chinese_count > 0:
            replaced_count = original_chinese_count - final_chinese_count
            replacement_rate = (replaced_count / original_chinese_count) * 100

        # 构建统计信息（保持原有格式）
        statistics = {
            "extraction_rate": 100.0,  # DOM提取率始终为100%
            "replacement_rate": replacement_rate,
            "damage_rate": 0.0,  # 假设无破坏
            "original_chinese_count": original_chinese_count,
            "replaced_count": stats.total_replacements,
            "final_chinese_count": final_chinese_count,
            "method": "ultimate_dom_v2"
        }

        pass

        return html_final, statistics

    def create_translation_map(self, translation_results: Dict) -> Dict[str, str]:
        """创建翻译映射表"""
        translation_map = {}

        if 'translations' in translation_results:
            for result in translation_results['translations']:
                if result.get('success', False):
                    original = result.get('original', '').strip()
                    translated = result.get('translated', '').strip()
                    if original and translated:
                        translation_map[original] = translated

        pass
        return translation_map

    def get_replacement_statistics(self, html_before: str, html_after: str) -> Dict:
        """获取替换统计信息"""
        original_chinese = self.chinese_pattern.findall(html_before)
        final_chinese = self.chinese_pattern.findall(html_after)

        original_count = len(original_chinese)
        final_count = len(final_chinese)
        replaced_count = original_count - final_count

        replacement_rate = 0.0
        if original_count > 0:
            replacement_rate = (replaced_count / original_count) * 100

        return {
            "original_chinese_count": original_count,
            "final_chinese_count": final_count,
            "replaced_count": replaced_count,
            "replacement_rate": replacement_rate,
            "unique_original": len(set(original_chinese)),
            "unique_final": len(set(final_chinese))
        }

    def _convert_text_node_to_dict(self, text_node) -> Dict:
        """将文本节点实体转换为字典（保持向后兼容）"""
        result = {
            'content': text_node.content,
            'chinese_texts': text_node.chinese_texts,
            'path': text_node.path,
            'element': text_node.element,
            'parent': text_node.parent
        }

        if text_node.is_attribute_text:
            result['attr_name'] = text_node.attr_name

        return result

    def _count_replacements(self, html_before: str, html_after: str) -> int:
        """计算替换次数"""
        chinese_before = self.chinese_pattern.findall(html_before)
        chinese_after = self.chinese_pattern.findall(html_after)
        return len(chinese_before) - len(chinese_after)


# 创建全局实例
dom_replacement_service_v2 = DomReplacementServiceV2()

# 为了保持向后兼容，创建一个适配器
class DomReplacementServiceAdapter:
    """DOM替换服务适配器 - 兼容原有接口"""

    def __init__(self):
        self.service = dom_replacement_service_v2

    def extract_all_chinese_with_dom(self, html_body: str, untranslatable_tags: str = None) -> Dict:
        """兼容原有接口"""
        return self.service.extract_all_chinese_with_dom(html_body, untranslatable_tags)

    def ultimate_replace_chinese(self, html_body: str, translation_map: Dict[str, str], untranslatable_tags: str = None) -> Tuple[str, Dict]:
        """兼容原有接口"""
        return self.service.ultimate_replace_chinese(html_body, translation_map, untranslatable_tags)

    def create_translation_map(self, translation_results: Dict) -> Dict[str, str]:
        """兼容原有接口"""
        return self.service.create_translation_map(translation_results)


# 创建适配器实例，替代原有的dom_replacement_service
dom_replacement_service_adapter = DomReplacementServiceAdapter()
