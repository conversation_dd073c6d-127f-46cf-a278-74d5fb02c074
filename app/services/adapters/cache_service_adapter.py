"""
缓存服务适配器
提供与原有缓存服务兼容的接口
"""

from typing import Optional, Dict, Any
from app.models.entities.cache_entity import CacheKey
from app.models.enums.translation_enums import LanguageCode, CacheStrategy
from app.services.infrastructure.file_cache_service import file_cache_service_v2
from app.services.infrastructure.redis_cache_service import redis_cache_service_v2


class FileCacheServiceAdapter:
    """文件缓存服务适配器 - 兼容原有接口"""

    def __init__(self):
        self.service = file_cache_service_v2

    async def get_cache(self, path: str, source_lang: str, target_lang: str) -> Optional[Dict]:
        """兼容原有的get_cache方法"""
        try:
            cache_key = CacheKey(
                path=path,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )
            return await self.service.get_cache(cache_key)
        except Exception as e:
            return None

    async def set_cache(self, path: str, source_lang: str, target_lang: str, data: Dict) -> bool:
        """兼容原有的set_cache方法"""
        try:
            cache_key = CacheKey(
                path=path,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )
            result = await self.service.set_cache(cache_key, data)
            return result.success
        except Exception as e:
            return False

    async def delete_cache(self, path: str, source_lang: str, target_lang: str) -> bool:
        """兼容原有的delete_cache方法"""
        try:
            cache_key = CacheKey(
                path=path,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )
            result = await self.service.delete_cache(cache_key)
            return result.success
        except Exception as e:
            return False

    async def exists(self, path: str, source_lang: str, target_lang: str) -> bool:
        """兼容原有的exists方法"""
        try:
            cache_key = CacheKey(
                path=path,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )
            return await self.service.exists_cache(cache_key)
        except Exception:
            return False

    async def clear_all_cache(self) -> bool:
        """兼容原有的clear_all_cache方法"""
        try:
            result = await self.service.clear_all_cache()
            return result.success
        except Exception:
            return False


class RedisCacheServiceAdapter:
    """Redis缓存服务适配器 - 兼容原有接口"""

    def __init__(self):
        self.service = redis_cache_service_v2

    async def initialize(self) -> bool:
        """初始化Redis连接"""
        return await self.service.initialize()

    async def get_cache(self, path: str, source_lang: str, target_lang: str) -> Optional[Dict]:
        """兼容原有的get_cache方法"""
        try:
            cache_key = CacheKey(
                path=path,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )
            return await self.service.get_cache(cache_key)
        except Exception as e:
            return None

    async def set_cache(self, path: str, source_lang: str, target_lang: str, data: Dict) -> bool:
        """兼容原有的set_cache方法"""
        try:
            cache_key = CacheKey(
                path=path,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )
            result = await self.service.set_cache(cache_key, data)
            return result.success
        except Exception as e:
            return False

    async def delete_cache(self, path: str, source_lang: str, target_lang: str) -> bool:
        """兼容原有的delete_cache方法"""
        try:
            cache_key = CacheKey(
                path=path,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )
            result = await self.service.delete_cache(cache_key)
            return result.success
        except Exception as e:
            return False

    async def exists(self, path: str, source_lang: str, target_lang: str) -> bool:
        """兼容原有的exists方法"""
        try:
            cache_key = CacheKey(
                path=path,
                source_language=LanguageCode(source_lang),
                target_language=LanguageCode(target_lang)
            )
            return await self.service.exists_cache(cache_key)
        except Exception:
            return False

    async def clear_all_cache(self) -> bool:
        """兼容原有的clear_all_cache方法"""
        try:
            result = await self.service.clear_all_cache()
            return result.success
        except Exception:
            return False

    async def get_cache_info(self) -> Dict[str, Any]:
        """兼容原有的get_cache_info方法"""
        try:
            stats = await self.service.get_statistics()
            return {
                "status": "connected",
                "total_entries": stats.total_entries,
                "hit_count": stats.hit_count,
                "miss_count": stats.miss_count,
                "hit_rate": stats.hit_rate
            }
        except Exception as e:
            return {"status": "error", "error": str(e)}

    async def close(self):
        """关闭Redis连接"""
        await self.service.close()


class CacheServiceFactory:
    """缓存服务工厂 - 根据策略创建适配器"""

    @staticmethod
    def create_cache_service(strategy: CacheStrategy):
        """根据策略创建缓存服务"""
        if strategy == CacheStrategy.FILE_CACHE:
            return FileCacheServiceAdapter()
        elif strategy == CacheStrategy.REDIS_CACHE:
            return RedisCacheServiceAdapter()
        else:
            raise ValueError(f"不支持的缓存策略: {strategy}")


# 创建适配器实例，替代原有的缓存服务
file_cache_service_adapter = FileCacheServiceAdapter()
redis_cache_service_adapter = RedisCacheServiceAdapter()

# 为了保持向后兼容，创建别名
file_cache_service_v2_compat = file_cache_service_adapter
redis_path_cache_service_v2_compat = redis_cache_service_adapter
