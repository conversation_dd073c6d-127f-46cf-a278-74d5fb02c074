"""
翻译引擎适配器
提供与原有百度翻译服务兼容的接口
"""

from typing import List, Dict, Any, Optional
from app.services.infrastructure.qwen_mt_translation_engine import qwen_mt_translation_engine
from app.models.entities.translation_engine_entity import TranslationRequest


class TranslationEngineAdapter:
    """翻译引擎适配器 - 兼容原有接口"""

    def __init__(self):
        self.engine = qwen_mt_translation_engine

    async def async_translate_text(self, session, text: str, from_lang: str, to_lang: str) -> Dict:
        """
        兼容原有的async_translate_text方法
        注意：session参数在新架构中不使用，但保持接口兼容性
        """
        if not self.engine:
            return {
                "success": False,
                "error": "翻译引擎未初始化",
                "original": text
            }

        request = TranslationRequest(
            text=text,
            from_lang=from_lang,
            to_lang=to_lang
        )

        response = await self.engine.translate_single(request)

        # 转换为原有格式
        if response.success:
            return {
                "success": True,
                "original": response.original,
                "translated": response.translated,
                "from_lang": response.from_lang,
                "to_lang": response.to_lang
            }
        else:
            return {
                "success": False,
                "error": response.error,
                "original": response.original
            }

    async def concurrent_batch_translate(
        self,
        texts: List[str],
        from_lang: str,
        to_lang: str,
        max_concurrent: Optional[int] = None  # 如果为None，使用配置中的值
    ) -> Dict:
        """兼容原有的concurrent_batch_translate方法"""
        if not self.engine:
            return {
                "translations": [],
                "success_count": 0,
                "failed_count": 0,
                "total_count": len(texts),
                "unique_count": 0,
                "duration": 0
            }

        result = await self.engine.translate_batch(texts, from_lang, to_lang, max_concurrent)

        # 转换为原有格式
        translations = []
        for response in result.translations:
            if response.success:
                translations.append({
                    "success": True,
                    "original": response.original,
                    "translated": response.translated,
                    "from_lang": response.from_lang,
                    "to_lang": response.to_lang
                })
            else:
                translations.append({
                    "success": False,
                    "error": response.error,
                    "original": response.original
                })

        return {
            "translations": translations,
            "success_count": result.success_count,
            "failed_count": result.failed_count,
            "total_count": result.total_count,
            "unique_count": result.unique_count,
            "duration": result.duration
        }

    def get_config_info(self) -> Dict:
        """兼容原有的get_config_info方法"""
        if not self.engine:
            return {
                "app_id": "",
                "secret_key_preview": "",
                "api_url": "",
                "timeout": 0
            }

        return self.engine.get_config_info()

    def _generate_sign(self, query: str, salt: str) -> str:
        """兼容原有的_generate_sign方法"""
        if not self.engine:
            return ""

        return self.engine.signature_generator.generate_sign(
            query, salt, self.engine.config.app_id, self.engine.config.secret_key
        )


# 创建适配器实例，替代原有的baidu_translation_service
translation_engine_adapter = TranslationEngineAdapter()

# 为了保持向后兼容，创建一个别名
baidu_translation_service_v2 = translation_engine_adapter
