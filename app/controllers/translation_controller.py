"""
翻译控制器 -
只负责HTTP请求处理，业务逻辑
"""

from fastapi import APIRouter, HTTPException, Form
import json
import time
from typing import Dict, Any
from app.dto.requests.translation_request_dto import TranslationRequestDTO
from app.dto.responses.translation_response_dto import TranslationResponseDTO
from app.dto.requests.language_detection_request_dto import LanguageDetectionRequestDTO
from app.dto.responses.language_detection_response_dto import LanguageDetectionResponseDTO
from app.dto.requests.json_translation_request_dto import JsonTranslationRequestDTO
from app.dto.responses.json_translation_response_dto import JsonTranslationResponseDTO


from app.models.entities.translation_entity import TranslationTask
from app.models.enums.translation_enums import LanguageCode, CacheStrategy
from app.services.application.translation_application_service import translation_application_service
from app.services.application.language_detection_service import language_detection_service
from app.services.application.json_translation_service import json_translation_service

from app.services.infrastructure.content_hash_cache_service import content_hash_cache_service
from app.services.infrastructure.sensitive_word_processor import sensitive_word_processor
from app.services.infrastructure.date_processor import date_processor
from app.services.infrastructure.city_processor import city_processor

router = APIRouter(prefix="/api", tags=["翻译"])


async def _preprocess_json_for_cache(json_data: Dict[str, Any], target_language: str) -> Dict[str, Any]:
    """
    为缓存预处理JSON数据（敏感词、日期、城市名处理）
    确保缓存键与实际翻译内容一致
    """
    import copy

    # 深拷贝避免修改原始数据
    processed_data = copy.deepcopy(json_data)

    def process_text_value(text: str) -> str:
        """处理单个文本值"""
        if not isinstance(text, str) or not text.strip():
            return text

        # 1. 敏感词处理
        processed_text = sensitive_word_processor.process_text(text)

        # 2. 日期处理
        processed_text = date_processor.process_text(processed_text)

        # 3. 城市名处理
        processed_text = city_processor.process_text(processed_text)

        return processed_text

    def process_json_recursive(obj):
        """递归处理JSON对象"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                if isinstance(value, str):
                    obj[key] = process_text_value(value)
                elif isinstance(value, (dict, list)):
                    process_json_recursive(value)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                if isinstance(item, str):
                    obj[i] = process_text_value(item)
                elif isinstance(item, (dict, list)):
                    process_json_recursive(item)

    # 处理JSON数据
    process_json_recursive(processed_data)

    return processed_data


@router.post("/translate", response_model=TranslationResponseDTO, summary="翻译接口 - 100%替换率")
async def translate_ultimate(request: TranslationRequestDTO):
    """
    终极翻译接口 - 使用DOM解析 + 多重替换策略，确保最高的替换成功率

    新增智能缓存功能：
    - cache_strategy: auto=自动选择，content_hash=内容哈希，path_time=路径+时间，path=传统路径
    - cache_ttl: 缓存TTL（秒）
    - force_refresh: 强制刷新缓存
    """
    try:
        # 1. 参数验证和转换
        task = _convert_request_to_task(request)

        # 2. 记录请求信息
        _log_translation_request(request, "终极翻译")
        _log_cache_strategy(request)

        # 3. 调用应用服务（内部已包含统一的缓存逻辑）
        result = await translation_application_service.translate_ultimate(task)

        # 6. 返回响应
        return TranslationResponseDTO(
            success=result["success"],
            message=result["message"],
            data=result["data"]
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"参数错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"翻译失败: {str(e)}")


@router.post("/safetrans", response_model=TranslationResponseDTO, summary="安全翻译接口 - 0%破坏率")
async def translate_safe(request: TranslationRequestDTO):
    """
    安全翻译接口 - 0%元素破坏率，保护JavaScript功能和DOM结构

    新增智能缓存功能：
    - cache_strategy: auto=自动选择，content_hash=内容哈希，path_time=路径+时间，path=传统路径
    - cache_ttl: 缓存TTL（秒）
    - force_refresh: 强制刷新缓存
    """
    try:
        # 1. 参数验证和转换
        task = _convert_request_to_task(request)

        # 2. 记录请求信息
        _log_translation_request(request, "安全翻译")
        _log_cache_strategy(request)

        # 3. 调用应用服务（内部已包含统一的缓存逻辑）
        result = await translation_application_service.translate_safe(task)

        # 6. 返回响应
        return TranslationResponseDTO(
            success=result["success"],
            message=result["message"],
            data=result["data"]
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"参数错误: {str(e)}")
    except ConnectionError as e:
        raise HTTPException(status_code=503, detail=f"翻译服务暂时不可用: {str(e)}")
    except TimeoutError as e:
        raise HTTPException(status_code=504, detail=f"翻译请求超时: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"安全翻译失败: {str(e)}")


@router.post("/detect-translate", response_model=LanguageDetectionResponseDTO, summary="语言识别翻译接口 - 自动识别语言并翻译成中文")
async def detect_and_translate(request: LanguageDetectionRequestDTO):
    """
    语言识别翻译接口

    自动识别输入文本的语言，并翻译成中文
    支持的语言：中文、英语、马来语、高棉语、印尼语、缅甸语、菲律宾语、泰语、越南语、泰米尔语、老挝语
    """
    import time
    start_time = time.time()

    try:
        # 1. 记录请求
        pass

        # 2. 调用语言识别翻译服务
        result = await language_detection_service.detect_and_translate_to_chinese(request.text)

        # 3. 计算处理时间
        processing_time = time.time() - start_time

        # 4. 转换为DTO并返回
        return LanguageDetectionResponseDTO(
            success=result["success"],
            original_text=result["original_text"],
            detected_language=result.get("detected_language"),
            detected_language_name=result.get("detected_language_name"),
            translated_text=result.get("translated_text"),
            message=result.get("message", "处理完成"),
            error=result.get("error"),
            processing_time=processing_time
        )

    except Exception as e:
        processing_time = time.time() - start_time
        return LanguageDetectionResponseDTO(
            success=False,
            original_text=request.text,
            detected_language=None,
            detected_language_name=None,
            translated_text=None,
            message="处理失败",
            error=f"语言识别翻译失败: {str(e)}",
            processing_time=processing_time
        )


@router.post("/translate-json", response_model=JsonTranslationResponseDTO, summary="JSON翻译接口 - 0瑕疵完美翻译")
async def translate_json(request: JsonTranslationRequestDTO):
    """
    JSON翻译接口 - 使用方案B分段翻译法，确保HTML标签100%完整

    特点：
    - 0瑕疵HTML标签处理
    - 支持复杂嵌套JSON结构
    - 智能缓存支持（可选）
    - 完美保持原始结构

    新增智能缓存功能：
    - cache_strategy: auto=自动选择，content_hash=内容哈希，path_time=路径+时间，none=不缓存
    - cache_ttl: 缓存TTL（秒）
    - force_refresh: 强制刷新缓存
    """
    import time
    import json
    start_time = time.time()

    try:
        # 1. 记录请求信息
        pass

        # 2. 记录缓存策略
        pass

        # 3. 预处理JSON数据（敏感词处理）
        processed_json_data = await _preprocess_json_for_cache(request.json_data, request.target_language)

        # 4. 智能缓存检查（使用预处理后的内容）
        cached_result = None
        if request.use_cache and not request.force_refresh:
            processed_json_content = json.dumps(processed_json_data, ensure_ascii=False, sort_keys=True)
            cached_result = await content_hash_cache_service.get_cache_by_content(
                path=request.path or "json_translation",
                content=processed_json_content,
                source_lang="zh",
                target_lang=request.target_language,
                cache_strategy=request.cache_strategy,
                use_redis=request.use_redis
            )

            if cached_result:
                total_processing_time = time.time() - start_time

                # 更新统计信息
                if "statistics" in cached_result:
                    cached_result["statistics"]["total_processing_time"] = total_processing_time
                    cached_result["statistics"]["from_cache"] = True

                return JsonTranslationResponseDTO(
                    success=True,
                    message=f"使用{request.cache_strategy}缓存结果！",
                    translated_data=cached_result.get("translated_data"),
                    statistics=cached_result.get("statistics"),
                    error=None
                )

        # 5. 调用JSON翻译服务（使用预处理后的数据）
        result = await json_translation_service.translate_json(
            json_data=processed_json_data,
            target_language=request.target_language
        )

        # 6. 智能缓存保存（使用预处理后的内容作为缓存键）
        if request.use_cache and result["success"]:
            processed_json_content = json.dumps(processed_json_data, ensure_ascii=False, sort_keys=True)
            await content_hash_cache_service.set_cache_by_content(
                path=request.path or "json_translation",
                content=processed_json_content,
                source_lang="zh",
                target_lang=request.target_language,
                data=result,
                cache_strategy=request.cache_strategy,
                ttl_seconds=request.cache_ttl,
                use_redis=request.use_redis
            )

        # 7. 计算总处理时间
        total_processing_time = time.time() - start_time
        if "statistics" in result:
            result["statistics"]["total_processing_time"] = total_processing_time
            result["statistics"]["from_cache"] = False

        # 7. 返回响应
        return JsonTranslationResponseDTO(
            success=result["success"],
            message=result["message"],
            translated_data=result.get("translated_data"),
            statistics=result.get("statistics"),
            error=result.get("error")
        )

    except ValueError as e:
        total_processing_time = time.time() - start_time
        return JsonTranslationResponseDTO(
            success=False,
            message="参数错误",
            error=f"参数验证失败: {str(e)}",
            statistics={"total_processing_time": total_processing_time}
        )
    except Exception as e:
        total_processing_time = time.time() - start_time
        return JsonTranslationResponseDTO(
            success=False,
            message="JSON翻译失败",
            error=f"翻译过程中发生错误: {str(e)}",
            statistics={"total_processing_time": total_processing_time}
        )



def _convert_request_to_task(request: TranslationRequestDTO) -> TranslationTask:
    """将请求转换为任务实体"""
    try:
        source_lang = LanguageCode(request.source_language)
        target_lang = LanguageCode(request.target_language)
        # 使用缓存存储策略（基于cache参数）
        cache_storage_strategy = request.cache_storage_enum

        return TranslationTask(
            path=request.path,
            html_body=request.html_body,
            source_language=source_lang,
            target_language=target_lang,
            untranslatable_tags=request.untranslatable_tags,
            no_translate_tags=request.no_translate_tags,
            cache_strategy=cache_storage_strategy  # 这里传递的是存储策略
        )
    except ValueError as e:
        raise ValueError(f"不支持的语言代码: {e}")


def _log_translation_request(request: TranslationRequestDTO, translation_type: str):
    """记录翻译请求信息"""
    pass


def _log_cache_strategy(request: TranslationRequestDTO):
    """记录缓存策略信息"""
    pass






