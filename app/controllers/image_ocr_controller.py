"""
图片 OCR 控制器
"""

from fastapi import APIRouter, HTTPException
from app.dto.requests.image_ocr_request_dto import ImageOCRRequestDTO
from app.dto.responses.image_ocr_response_dto import ImageOCRResponseDTO
from app.dto.requests.batch_image_ocr_request_dto import Batch<PERSON>mageOCRRequestDTO
from app.dto.responses.batch_image_ocr_response_dto import BatchImageOCRResponseDTO
from app.services.application.image_ocr_service import image_ocr_service


router = APIRouter(prefix="/api", tags=["图片翻译"])


@router.post("/images", response_model=ImageOCRResponseDTO, summary="图片识别翻译接口")
async def process_image_ocr(request: ImageOCRRequestDTO):
    """
    图片识别翻译接口

    接收 Base64 编码的图片和目标语言，识别图片中的中文并翻译，返回原图片和翻译结果

    功能：
    - 识别图片中的所有文字
    - 过滤出中文文字
    - 将中文翻译成目标语言
    - 返回原始 Base64 图片数据
    - 返回翻译后的文字列表

    参数：
    - base64_image: Base64 编码的图片数据
    - target_language: 目标语言代码（如 "en", "may", "th" 等）
    """
    try:
        # 调用应用服务处理请求
        result = await image_ocr_service.process_image_ocr(request)
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"参数错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图片识别处理失败: {str(e)}")


@router.post("/images/batch", response_model=BatchImageOCRResponseDTO, summary="批量图片识别翻译接口")
async def process_batch_image_ocr(request: BatchImageOCRRequestDTO):
    """
    批量图片识别翻译接口

    接收多张 Base64 编码的图片和目标语言，并发识别图片中的中文并翻译，返回所有图片的处理结果

    功能：
    - 并发处理多张图片
    - 识别每张图片中的所有文字
    - 过滤出中文文字
    - 将中文翻译成目标语言
    - 支持图片缓存（可配置缓存时间）
    - 返回详细的批量处理统计信息

    参数：
    - images: Base64 编码的图片数据列表
    - target_language: 目标语言代码（如 "en", "may", "th" 等）
    - max_concurrent: 最大并发处理数量（1-20，默认5）
    - use_cache: 是否使用缓存（默认true）
    - cache_ttl_minutes: 缓存时间（分钟），-1表示永久缓存，0表示不缓存（默认-1）

    特性：
    - 无上限图片数量（建议单次不超过100张）
    - 智能缓存：相同图片+语言组合会复用缓存结果
    - 并发控制：避免同时处理过多图片导致资源耗尽
    - 详细统计：成功率、缓存命中率、处理时间等
    """
    try:
        # 调用应用服务处理批量请求
        result = await image_ocr_service.process_batch_image_ocr(request)
        return result

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"参数错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量图片识别处理失败: {str(e)}")
