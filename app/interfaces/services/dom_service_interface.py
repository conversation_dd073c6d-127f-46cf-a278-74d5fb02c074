"""
DOM处理服务接口
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple
from app.models.entities.dom_entity import (
    DomExtractionData,
    DomProcessingConfig,
    ReplacementResult,
    ReplacementStatistics,
    CssSelector
)


class IDomParser(ABC):
    """DOM解析器接口"""
    
    @abstractmethod
    def parse_html(self, html_content: str) -> any:
        """解析HTML内容"""
        pass
    
    @abstractmethod
    def extract_text_nodes(self, soup: any, config: DomProcessingConfig) -> List:
        """提取文本节点"""
        pass


class ITextExtractor(ABC):
    """文本提取器接口"""
    
    @abstractmethod
    def extract_chinese_texts(self, html_content: str, config: DomProcessingConfig) -> DomExtractionData:
        """提取中文文本"""
        pass
    
    @abstractmethod
    def find_chinese_in_text(self, text: str) -> List[str]:
        """在文本中查找中文"""
        pass


class ITextReplacer(ABC):
    """文本替换器接口"""
    
    @abstractmethod
    def replace_in_dom(self, dom_data: DomExtractionData, translation_map: Dict[str, str]) -> str:
        """在DOM中替换文本"""
        pass
    
    @abstractmethod
    def replace_special_cases(self, html_content: str, translation_map: Dict[str, str]) -> Tuple[str, int]:
        """处理特殊情况的替换"""
        pass
    
    @abstractmethod
    def brute_force_replace(self, html_content: str, translation_map: Dict[str, str], config: DomProcessingConfig) -> Tuple[str, int]:
        """暴力替换"""
        pass


class ISelectorProcessor(ABC):
    """选择器处理器接口"""
    
    @abstractmethod
    def parse_selectors(self, selector_string: str) -> List[CssSelector]:
        """解析选择器字符串"""
        pass
    
    @abstractmethod
    def should_process_element(self, element: any, selectors: List[CssSelector], soup: any) -> bool:
        """判断元素是否应该被处理"""
        pass


class IDomReplacementService(ABC):
    """DOM替换服务主接口"""
    
    @abstractmethod
    def extract_all_chinese_with_dom(self, html_body: str, untranslatable_tags: str = None) -> Dict:
        """提取所有中文文本"""
        pass
    
    @abstractmethod
    def ultimate_replace_chinese(self, html_body: str, translation_map: Dict[str, str], untranslatable_tags: str = None) -> Tuple[str, Dict]:
        """终极替换中文"""
        pass
    
    @abstractmethod
    def create_translation_map(self, translation_results: Dict) -> Dict[str, str]:
        """创建翻译映射表"""
        pass


class IUltraSafeDomService(ABC):
    """超安全DOM服务接口"""
    
    @abstractmethod
    def ultra_safe_replacement(self, html_body: str, translation_map: Dict[str, str], untranslatable_tags: str = None) -> Tuple[str, Dict]:
        """超安全替换"""
        pass
    
    @abstractmethod
    def is_safe_element(self, element: any) -> bool:
        """判断元素是否安全"""
        pass
