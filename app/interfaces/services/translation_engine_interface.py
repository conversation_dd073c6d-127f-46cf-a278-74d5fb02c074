"""
翻译引擎接口
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from app.models.entities.translation_engine_entity import (
    TranslationEngineConfig,
    TranslationRequest,
    TranslationResponse,
    BatchTranslationResult
)


class ITranslationEngine(ABC):
    """翻译引擎抽象接口"""
    
    @abstractmethod
    async def translate_single(self, request: TranslationRequest) -> TranslationResponse:
        """翻译单个文本"""
        pass
    
    @abstractmethod
    async def translate_batch(
        self,
        texts: List[str],
        from_lang: str,
        to_lang: str,
        max_concurrent: Optional[int] = None  # 如果为None，使用配置中的值
    ) -> BatchTranslationResult:
        """批量翻译"""
        pass
    
    @abstractmethod
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """验证配置"""
        pass


class ISignatureGenerator(ABC):
    """签名生成器接口"""
    
    @abstractmethod
    def generate_sign(self, query: str, salt: str, app_id: str, secret_key: str) -> str:
        """生成API签名"""
        pass


class IApiClient(ABC):
    """API客户端接口"""
    
    @abstractmethod
    async def call_api(self, params: Dict[str, str], timeout: float) -> Dict[str, Any]:
        """调用外部API"""
        pass


class ITextProcessor(ABC):
    """文本处理器接口"""
    
    @abstractmethod
    def preprocess_text(self, text: str) -> str:
        """预处理文本"""
        pass
    
    @abstractmethod
    def postprocess_text(self, text: str) -> str:
        """后处理文本"""
        pass
    
    @abstractmethod
    def deduplicate_texts(self, texts: List[str]) -> tuple[List[str], Dict[str, int]]:
        """文本去重"""
        pass
