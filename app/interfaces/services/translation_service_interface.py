"""
翻译服务接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from app.models.entities.translation_entity import TranslationTask, TranslationResult, TranslationStatistics


class ITranslationService(ABC):
    """翻译服务接口"""
    
    @abstractmethod
    async def translate_ultimate(self, task: TranslationTask) -> Dict[str, Any]:
        """终极翻译 - 100%替换率"""
        pass
    
    @abstractmethod
    async def translate_safe(self, task: TranslationTask) -> Dict[str, Any]:
        """安全翻译 - 0%破坏率"""
        pass
    
    @abstractmethod
    async def get_cached_result(self, task: TranslationTask) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        pass
    
    @abstractmethod
    async def save_translation_result(self, task: TranslationTask, result: Dict[str, Any]) -> bool:
        """保存翻译结果到缓存"""
        pass


class ICacheService(ABC):
    """缓存服务接口"""
    
    @abstractmethod
    async def get_cache(self, path: str, source_lang: str, target_lang: str) -> Optional[Dict[str, Any]]:
        """获取缓存"""
        pass
    
    @abstractmethod
    async def set_cache(self, path: str, source_lang: str, target_lang: str, data: Dict[str, Any]) -> bool:
        """设置缓存"""
        pass
    
    @abstractmethod
    async def clear_cache(self, path: str, source_lang: str, target_lang: str) -> bool:
        """清除缓存"""
        pass


class ITranslationEngine(ABC):
    """翻译引擎接口"""
    
    @abstractmethod
    async def translate_text(self, text: str, from_lang: str, to_lang: str) -> TranslationResult:
        """翻译单个文本"""
        pass
    
    @abstractmethod
    async def batch_translate(self, texts: list, from_lang: str, to_lang: str) -> Dict[str, Any]:
        """批量翻译"""
        pass
