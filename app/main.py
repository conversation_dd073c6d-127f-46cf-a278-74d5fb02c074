"""
API应用主文件
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from app.controllers.translation_controller import router as translation_router
from app.controllers.image_ocr_controller import router as image_ocr_router
from app.services.adapters.cache_service_adapter import redis_cache_service_adapter
from app.services.infrastructure.cache_cleanup_scheduler import cache_cleanup_scheduler




@asynccontextmanager
async def lifespan(app: FastAPI):
    print(" 启动翻译API服务...")

    # 初始化Redis缓存服务
    try:
        redis_success = await redis_cache_service_adapter.initialize()
        if redis_success:
            # 获取Redis连接信息
            redis_info = await redis_cache_service_adapter.get_cache_info()
            print(f"   Redis状态: {redis_info.get('status', 'unknown')}")
        else:
            print("   Redis连接失败，使用文件缓存")
    except Exception as e:
        print(f"   Redis初始化异常: {e}")
    # 初始化缓存清理调度器
    try:
        cleanup_success = await cache_cleanup_scheduler.initialize()
        if cleanup_success:
            cache_info = await cache_cleanup_scheduler.get_cache_info()
            from app.config.config import get_settings
            settings = get_settings()
            print(f"   翻译缓存清理: 每{settings.translation_cache_cleanup_interval_minutes}分钟")
            print(f"   图片缓存清理: 每{settings.image_cache_cleanup_interval_minutes}分钟")
            print(f"   当前缓存文件: {cache_info.get('total_files', 0)} 个")
            print(f"   缓存大小: {cache_info.get('total_size_mb', 0)} MB")
            next_cleanup = cache_info.get('next_cleanup')
            if next_cleanup:
                print(f"   下次清理时间: {next_cleanup}")
        else:
            print("   缓存清理启动失败")
    except Exception as e:
        print(f"   缓存清理初始化异常: {e}")



    print("服务启动完成！")

    yield

    # 关闭时清理连接
    print("正在关闭服务...")

    try:
        await redis_cache_service_adapter.close()
        print("Redis缓存服务已清理")
    except Exception as e:
        print(f"Redis清理过程中出现错误: {e}")

    # 关闭缓存清理调度器
    try:
        await cache_cleanup_scheduler.shutdown()
        print("缓存清理调度器已关闭")
    except Exception as e:
        print(f"缓存清理调度器关闭失败: {e}")

    print("服务已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="高速并发翻译API",
    description="基于翻译API的高速并发翻译服务",
    version="2.0.0",
    docs_url=None,
    redoc_url=None,
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(translation_router)  # 翻译API
app.include_router(image_ocr_router)    # 图片OCR API

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")


@app.get("/")
async def root():
    """根路径 - 返回文档首页"""
    return FileResponse("static/index.html")

@app.get("/README.md")
async def readme_file():
    """提供README.md文件"""
    return FileResponse("README.md", media_type="text/markdown")

@app.get("/health")
async def health():
    """健康检查"""
    return {"status": "healthy", "version": "2.0.0"}
