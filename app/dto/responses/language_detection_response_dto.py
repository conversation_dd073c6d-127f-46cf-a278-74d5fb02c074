"""
语言识别翻译响应DTO
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class LanguageDetectionResponseDTO(BaseModel):
    """语言识别翻译响应DTO"""
    success: bool = Field(..., description="处理是否成功")
    original_text: str = Field(..., description="原始文本")
    detected_language: Optional[str] = Field(None, description="检测到的语言代码")
    detected_language_name: Optional[str] = Field(None, description="检测到的语言名称")
    translated_text: Optional[str] = Field(None, description="翻译后的中文文本")
    message: str = Field(..., description="处理消息")
    error: Optional[str] = Field(None, description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        json_schema_extra = {
            "example": {
                "success": True,
                "original_text": "Hello world",
                "detected_language": "en",
                "detected_language_name": "英语",
                "translated_text": "你好世界",
                "message": "从英语翻译成中文",
                "error": None,
                "timestamp": "2024-01-01T12:00:00",
                "processing_time": 1.5
            }
        }
