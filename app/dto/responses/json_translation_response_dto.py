"""
JSON翻译响应DTO
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, Optional


class JsonTranslationResponseDTO(BaseModel):
    """JSON翻译响应数据传输对象"""
    
    success: bool = Field(
        ...,
        description="翻译是否成功"
    )
    
    message: str = Field(
        ...,
        description="响应消息"
    )
    
    translated_data: Optional[Dict[str, Any]] = Field(
        None,
        description="翻译后的JSON数据"
    )
    
    statistics: Optional[Dict[str, Any]] = Field(
        None,
        description="翻译统计信息",
        example={
            "total_fields": 50,
            "translated_fields": 30,
            "chinese_fields_remaining": 0,
            "success_rate": 100.0,
            "processing_time": 2.5
        }
    )
    
    error: Optional[str] = Field(
        None,
        description="错误信息（如果有）"
    )
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "JSON翻译完成",
                "translated_data": {
                    "msg": "Operation successful",
                    "data": {
                        "searchInfo": {
                            "searchWord": "Business sector",
                            "hitWord": "Business sector"
                        },
                        "middle": {
                            "listAndBox": [
                                {
                                    "data": {
                                        "table-2": "2025 Guangxi Government Office Document",
                                        "table-3": "Office of the People's Government of Guangxi Zhuang Autonomous Region",
                                        "table-7": "<em>area</em>Autonomous Region Finance Department",
                                        "title": "Notice on Issuing<br/>Implementation Plan",
                                        "table-11": ["Finance, accounting, and auditing"]
                                    }
                                }
                            ]
                        }
                    }
                },
                "statistics": {
                    "total_fields": 8,
                    "translated_fields": 8,
                    "chinese_fields_remaining": 0,
                    "success_rate": 100.0,
                    "processing_time": 2.3
                }
            }
        }
