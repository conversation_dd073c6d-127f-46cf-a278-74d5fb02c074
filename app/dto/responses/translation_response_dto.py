"""
翻译响应DTO
从原有的models中抽取并增强
"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime


class TranslationStatisticsDTO(BaseModel):
    """翻译统计DTO"""
    total_texts: int = Field(..., description="总文本数")
    unique_texts: int = Field(..., description="唯一文本数")
    success_count: int = Field(..., description="成功翻译数")
    failed_count: int = Field(..., description="失败翻译数")
    processing_time: float = Field(..., description="处理时间（秒）")
    translation_duration: float = Field(..., description="翻译耗时（秒）")
    replacement_rate: float = Field(..., description="替换率（%）")
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.unique_texts == 0:
            return 0.0
        return (self.success_count / self.unique_texts) * 100
    
    @property
    def translation_speed(self) -> float:
        """翻译速度（文本/秒）"""
        if self.translation_duration == 0:
            return 0.0
        return self.unique_texts / self.translation_duration


class ReplacementStatisticsDTO(BaseModel):
    """替换统计DTO"""
    extraction_rate: float = Field(..., description="提取率（%）")
    replacement_rate: float = Field(..., description="替换率（%）")
    damage_rate: float = Field(..., description="破坏率（%）")
    original_chinese_count: int = Field(..., description="原始中文数量")
    replaced_count: int = Field(..., description="已替换数量")
    final_chinese_count: int = Field(..., description="最终中文数量")
    method: str = Field("unknown", description="替换方法")


class TranslationResultDTO(BaseModel):
    """翻译结果DTO"""
    original_html_body: str = Field(..., description="原始HTML")
    translated_html_body: str = Field(..., description="翻译后HTML")
    replacement_statistics: ReplacementStatisticsDTO = Field(..., description="替换统计")
    translation_map: Dict[str, str] = Field(..., description="翻译映射表")


class TranslationResponseDataDTO(BaseModel):
    """翻译响应数据DTO"""
    request_info: Dict[str, Any] = Field(..., description="请求信息")
    dom_extraction_results: Optional[Dict[str, Any]] = Field(None, description="DOM提取结果")
    translation_results: Optional[Dict[str, Any]] = Field(None, description="翻译结果")
    ultimate_replacement_results: Optional[TranslationResultDTO] = Field(None, description="终极替换结果")
    ultra_safe_replacement_results: Optional[TranslationResultDTO] = Field(None, description="安全替换结果")
    large_html_results: Optional[Dict[str, Any]] = Field(None, description="大型HTML处理结果")


class TranslationResponseDTO(BaseModel):
    """翻译响应DTO - 增强版"""
    success: bool = Field(True, description="处理是否成功")
    message: str = Field("处理完成", description="处理消息")
    data: Optional[TranslationResponseDataDTO] = Field(None, description="处理结果数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    processing_time: Optional[float] = Field(None, description="总处理时间（秒）")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class BatchTranslationResponseDTO(BaseModel):
    """批量翻译响应DTO"""
    success: bool = Field(True, description="批量处理是否成功")
    message: str = Field("批量处理完成", description="处理消息")
    results: List[TranslationResponseDTO] = Field(..., description="批量翻译结果")
    total_count: int = Field(..., description="总请求数")
    success_count: int = Field(..., description="成功数")
    failed_count: int = Field(..., description="失败数")
    total_processing_time: float = Field(..., description="总处理时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    
    @property
    def batch_success_rate(self) -> float:
        """批量成功率"""
        if self.total_count == 0:
            return 0.0
        return (self.success_count / self.total_count) * 100
