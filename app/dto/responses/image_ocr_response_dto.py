"""
图片 OCR 响应 DTO
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class ImageOCRResponseDTO(BaseModel):
    """图片 OCR 响应数据传输对象"""
    
    success: bool = Field(True, description="处理是否成功")
    message: str = Field("OCR 识别完成", description="处理消息")
    
    # 返回数据
    base64_image: Optional[str] = Field(None, description="处理后的 Base64 图片数据")

    # 图片识别结果
    ocr_raw_texts: List[str] = Field(default_factory=list, description="识别的所有原始文字")
    chinese_texts: List[str] = Field(default_factory=list, description="过滤出的中文文字")
    translated_texts: List[str] = Field(default_factory=list, description="翻译后的文字")

    # 兼容旧字段
    recognized_texts: List[str] = Field(default_factory=list, description="识别到的文字列表（兼容字段）")
    
    # 统计信息
    text_count: int = Field(0, description="识别到的文字块数量")
    processing_time: Optional[float] = Field(None, description="处理时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    
    # 错误信息
    error: Optional[str] = Field(None, description="错误信息")
    
    @property
    def combined_chinese_text(self) -> str:
        """合并所有中文文字"""
        return " ".join(self.chinese_texts)

    @property
    def combined_translated_text(self) -> str:
        """合并所有翻译文字"""
        return " ".join(self.translated_texts)

    @property
    def has_chinese_text(self) -> bool:
        """是否识别到中文文字"""
        return len(self.chinese_texts) > 0

    @property
    def combined_text(self) -> str:
        """合并所有识别文字（兼容）"""
        return " ".join(self.recognized_texts)

    @property
    def has_text(self) -> bool:
        """是否识别到文字（兼容）"""
        return len(self.recognized_texts) > 0
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "图片识别和翻译完成",
                "base64_image": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                "ocr_raw_texts": [
                    "广西壮族自治区人民政",
                    "Gvangjsih",
                    "BouxcuenghSwcigihYinzminzCwn"
                ],
                "chinese_texts": [
                    "广西壮族自治区人民政"
                ],
                "translated_texts": [
                    "Guangxi Zhuang Autonomous Region People's Government"
                ],
                "recognized_texts": [
                    "Guangxi Zhuang Autonomous Region People's Government"
                ],
                "text_count": 1,
                "processing_time": 2.5,
                "timestamp": "2025-07-01T16:30:00",
                "error": None
            }
        }
