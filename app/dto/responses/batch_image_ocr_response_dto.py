"""
批量图片 OCR 响应 DTO
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class ImageOCRResult(BaseModel):
    """单个图片OCR结果"""
    
    index: int = Field(..., description="图片在批量请求中的索引")
    success: bool = Field(..., description="该图片处理是否成功")
    message: str = Field(..., description="处理消息")
    
    # 图片数据
    base64_image: Optional[str] = Field(None, description="原始Base64图片数据")
    
    # OCR结果
    ocr_raw_texts: List[str] = Field(default_factory=list, description="识别的所有原始文字")
    chinese_texts: List[str] = Field(default_factory=list, description="过滤出的中文文字")
    translated_texts: List[str] = Field(default_factory=list, description="翻译后的文字")
    
    # 兼容字段
    recognized_texts: List[str] = Field(default_factory=list, description="识别到的文字列表（兼容字段）")
    
    # 统计信息
    text_count: int = Field(0, description="识别到的文字块数量")
    processing_time: Optional[float] = Field(None, description="单张图片处理时间（秒）")
    
    # 缓存信息
    from_cache: bool = Field(False, description="是否来自缓存")
    cache_key: Optional[str] = Field(None, description="缓存键")
    
    # 错误信息
    error: Optional[str] = Field(None, description="错误信息")


class BatchImageOCRResponseDTO(BaseModel):
    """批量图片 OCR 响应数据传输对象"""
    
    success: bool = Field(True, description="批量处理是否成功")
    message: str = Field("批量图片识别和翻译完成", description="处理消息")
    
    # 批量处理结果
    results: List[ImageOCRResult] = Field(default_factory=list, description="每张图片的处理结果")
    
    # 统计信息
    total_images: int = Field(0, description="总图片数量")
    successful_images: int = Field(0, description="成功处理的图片数量")
    failed_images: int = Field(0, description="失败的图片数量")
    cached_images: int = Field(0, description="来自缓存的图片数量")
    
    # 时间统计
    total_processing_time: Optional[float] = Field(None, description="总处理时间（秒）")
    average_processing_time: Optional[float] = Field(None, description="平均每张图片处理时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间戳")
    
    # 并发配置
    max_concurrent: int = Field(5, description="使用的最大并发数")
    
    # 缓存配置
    cache_enabled: bool = Field(True, description="是否启用了缓存")
    cache_ttl_minutes: int = Field(-1, description="缓存TTL（分钟）")
    
    # 错误信息
    errors: List[str] = Field(default_factory=list, description="处理过程中的错误列表")
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_images == 0:
            return 0.0
        return (self.successful_images / self.total_images) * 100
    
    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        if self.total_images == 0:
            return 0.0
        return (self.cached_images / self.total_images) * 100
    
    @property
    def total_recognized_texts(self) -> List[str]:
        """所有识别到的文字"""
        all_texts = []
        for result in self.results:
            if result.success:
                all_texts.extend(result.translated_texts)
        return all_texts
    
    @property
    def combined_translated_text(self) -> str:
        """合并所有翻译文字"""
        return " ".join(self.total_recognized_texts)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "批量图片识别和翻译完成",
                "results": [
                    {
                        "index": 0,
                        "success": True,
                        "message": "图片识别和翻译完成",
                        "base64_image": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                        "ocr_raw_texts": ["广西壮族自治区"],
                        "chinese_texts": ["广西壮族自治区"],
                        "translated_texts": ["Guangxi Zhuang Autonomous Region"],
                        "recognized_texts": ["Guangxi Zhuang Autonomous Region"],
                        "text_count": 1,
                        "processing_time": 2.5,
                        "from_cache": False,
                        "cache_key": "img_cache_abc123",
                        "error": None
                    }
                ],
                "total_images": 2,
                "successful_images": 2,
                "failed_images": 0,
                "cached_images": 0,
                "total_processing_time": 5.2,
                "average_processing_time": 2.6,
                "timestamp": "2025-07-01T16:30:00",
                "max_concurrent": 5,
                "cache_enabled": True,
                "cache_ttl_minutes": -1,
                "errors": []
            }
        }
