"""
批量图片 OCR 请求 DTO
"""

from pydantic import BaseModel, Field, validator
from typing import List


class BatchImageOCRRequestDTO(BaseModel):
    """批量图片 OCR 请求数据传输对象"""
    
    images: List[str] = Field(
        ...,
        description="Base64 编码的图片数据列表",
        min_items=1
    )
    
    target_language: str = Field(
        ...,
        description="目标语言代码",
        example="en"
    )
    
    @validator('images')
    def validate_images(cls, v):
        """验证图片列表"""
        if not v or len(v) == 0:
            raise ValueError("图片列表不能为空")
        
        if len(v) > 100:  # 设置合理的上限
            raise ValueError("单次最多处理100张图片")
        
        # 验证每个图片的Base64格式
        import base64
        for i, image_data in enumerate(v):
            if not image_data or len(image_data.strip()) == 0:
                raise ValueError(f"第{i+1}张图片数据不能为空")
            
            # 移除可能的 data:image 前缀进行验证
            clean_base64 = image_data
            if image_data.startswith('data:image'):
                clean_base64 = image_data.split(',')[1] if ',' in image_data else image_data
            
            try:
                base64.b64decode(clean_base64, validate=True)
            except Exception:
                raise ValueError(f"第{i+1}张图片的Base64数据无效")
        
        return v
    
    @validator('target_language')
    def validate_target_language(cls, v):
        """验证目标语言"""
        supported_languages = [
            'en', 'zh', 'may', 'hkm', 'id', 'bur', 'fil', 'th', 'vie', 'tam', 'lao'
        ]
        
        if v not in supported_languages:
            raise ValueError(f"不支持的目标语言: {v}，支持的语言: {supported_languages}")
        
        return v
    
    @property
    def total_images(self) -> int:
        """图片总数"""
        return len(self.images)
    
    class Config:
        json_schema_extra = {
            "example": {
                "images": [
                    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
                ],
                "target_language": "en"
            }
        }
