"""
语言识别翻译请求DTO
"""

from pydantic import BaseModel, Field, validator


class LanguageDetectionRequestDTO(BaseModel):
    """语言识别翻译请求DTO"""
    text: str = Field(..., description="要识别和翻译的文本", min_length=1, max_length=10000)
    
    @validator('text')
    def validate_text(cls, v):
        """验证文本内容"""
        if not v or not v.strip():
            raise ValueError("文本内容不能为空")
        
        # 检查文本长度
        if len(v.strip()) > 10000:
            raise ValueError("文本长度不能超过10000个字符")
            
        return v.strip()
    
    class Config:
        json_schema_extra = {
            "example": {
                "text": "Hello world"
            }
        }
