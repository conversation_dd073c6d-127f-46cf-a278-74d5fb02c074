"""
JSON翻译请求DTO
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, Any, Optional


class JsonTranslationRequestDTO(BaseModel):
    """JSON翻译请求数据传输对象"""
    
    target_language: str = Field(
        ...,
        description="目标语言代码",
        example="en"
    )
    
    json_data: Dict[str, Any] = Field(
        ...,
        description="要翻译的JSON数据",
        example={
            "msg": "操作成功",
            "data": {
                "title": "这是一个标题",
                "content": "这是内容"
            }
        }
    )

    # 新增智能缓存参数
    cache_strategy: str = Field("auto", description="缓存策略：auto=自动选择，content_hash=内容哈希，path_time=路径+时间，none=不缓存")
    cache_ttl: int = Field(3600, description="缓存TTL（秒），默认1小时", ge=60, le=86400)
    force_refresh: bool = Field(False, description="强制刷新缓存，忽略现有缓存")
    use_redis: bool = Field(False, description="使用Redis缓存，默认使用文件缓存")
    path: Optional[str] = Field(None, description="可选的路径标识，用于缓存分组")
    
    @validator('cache_strategy')
    def validate_cache_strategy(cls, v):
        """验证缓存策略"""
        valid_strategies = ["auto", "content_hash", "path_time", "none"]
        if v not in valid_strategies:
            raise ValueError(f"不支持的缓存策略: {v}，支持的策略: {valid_strategies}")
        return v

    @property
    def use_cache(self) -> bool:
        """是否使用缓存"""
        return self.cache_strategy != "none"

    @property
    def content_size(self) -> int:
        """JSON内容大小"""
        import json
        return len(json.dumps(self.json_data, ensure_ascii=False))

    @property
    def cache_info(self) -> Dict[str, Any]:
        """获取缓存配置信息"""
        return {
            "strategy": self.cache_strategy,
            "ttl": self.cache_ttl,
            "force_refresh": self.force_refresh,
            "use_redis": self.use_redis,
            "content_size": self.content_size,
            "path": self.path or "json_translation"
        }

    class Config:
        json_schema_extra = {
            "example": {
                "target_language": "en",
                "cache_strategy": "auto",
                "cache_ttl": 3600,
                "force_refresh": False,
                "use_redis": False,
                "path": "/api/data",
                "json_data": {
                    "msg": "操作成功",
                    "data": {
                        "searchInfo": {
                            "searchWord": "商务领域",
                            "hitWord": "商务领域"
                        },
                        "middle": {
                            "listAndBox": [
                                {
                                    "data": {
                                        "table-2": "2025年桂政办文件",
                                        "table-3": "广西壮族自治区人民政府办公厅",
                                        "table-7": "<em>领域</em>自治区财政厅",
                                        "title": "关于印发<br/>实施方案的通知",
                                        "table-11": ["财政、金融、审计"]
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }
