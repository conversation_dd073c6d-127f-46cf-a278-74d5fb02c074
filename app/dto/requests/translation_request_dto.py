"""
翻译请求DTO
从原有的models中抽取并增强
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from app.models.enums.translation_enums import LanguageCode, CacheStrategy


class TranslationRequestDTO(BaseModel):
    """翻译请求DTO - 增强版"""
    path: str = Field(..., description="路径（域名+后缀）", min_length=1)
    html_body: str = Field(..., description="HTML整个页面的body", min_length=1)
    source_language: str = Field(..., description="源语言代码")
    target_language: str = Field(..., description="目标语言代码")
    untranslatable_tags: Optional[str] = Field(None, description="允许翻译的标签（CSS选择器）")
    no_translate_tags: Optional[str] = Field(None, description="不需要翻译的标签")
    cache: bool = Field(True, description="缓存策略：true=文件缓存，false=Redis缓存")

    # 新增智能缓存参数
    cache_strategy: str = Field("auto", description="缓存策略：auto=自动选择，content_hash=内容哈希，path_time=路径+时间，path=传统路径")
    cache_ttl: int = Field(3600, description="缓存TTL（秒），默认1小时", ge=60, le=86400)
    force_refresh: bool = Field(False, description="强制刷新缓存，忽略现有缓存")
    
    @validator('source_language')
    def validate_source_language(cls, v):
        """验证源语言"""
        try:
            LanguageCode(v)
            return v
        except ValueError:
            raise ValueError(f"不支持的源语言: {v}")
    
    @validator('target_language')
    def validate_target_language(cls, v):
        """验证目标语言"""
        try:
            LanguageCode(v)
            return v
        except ValueError:
            raise ValueError(f"不支持的目标语言: {v}")

    @validator('cache_strategy')
    def validate_cache_strategy(cls, v):
        """验证缓存策略"""
        valid_strategies = ["auto", "content_hash", "path_time", "path", "force_content_hash", "path_content_hash"]
        if v not in valid_strategies:
            raise ValueError(f"不支持的缓存策略: {v}，支持的策略: {valid_strategies}")
        return v
    
    @validator('html_body')
    def validate_html_body(cls, v):
        """验证HTML内容"""
        if len(v.strip()) == 0:
            raise ValueError("HTML内容不能为空")
        return v
    
    @property
    def html_length(self) -> int:
        """HTML内容长度"""
        return len(self.html_body)
    
    @property
    def is_large_html(self) -> bool:
        """是否为大型HTML"""
        return self.html_length > 100000
    
    @property
    def cache_storage_enum(self) -> CacheStrategy:
        """获取缓存存储策略枚举（基于cache参数）"""
        return CacheStrategy.FILE_CACHE if self.cache else CacheStrategy.REDIS_CACHE

    @property
    def use_content_hash_cache(self) -> bool:
        """是否使用内容哈希缓存（基于cache_strategy参数）"""
        return self.cache_strategy in ["auto", "content_hash"]

    @property
    def use_redis_storage(self) -> bool:
        """是否使用Redis存储"""
        return not self.cache

    @property
    def effective_cache_strategy(self) -> str:
        """获取有效的缓存策略"""
        if self.cache_strategy == "auto":
            # 终极方案：所有内容都使用content_hash，永不重复翻译
            return "content_hash"
        return self.cache_strategy

    @property
    def cache_info(self) -> Dict[str, Any]:
        """获取缓存配置信息"""
        return {
            "storage": "redis" if self.use_redis_storage else "file",
            "algorithm": self.cache_strategy,
            "effective_algorithm": self.effective_cache_strategy,
            "ttl": self.cache_ttl,
            "force_refresh": self.force_refresh,
            "content_size": len(self.html_body),
            "use_content_hash": self.use_content_hash_cache
        }


class BatchTranslationRequestDTO(BaseModel):
    """批量翻译请求DTO"""
    requests: list[TranslationRequestDTO] = Field(..., description="批量翻译请求列表")
    max_concurrent: int = Field(5, description="最大并发数", ge=1, le=20)
    
    @validator('requests')
    def validate_requests(cls, v):
        """验证请求列表"""
        if len(v) == 0:
            raise ValueError("请求列表不能为空")
        if len(v) > 50:
            raise ValueError("批量请求数量不能超过50个")
        return v
