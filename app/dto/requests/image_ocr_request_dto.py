"""
图片 OCR 请求 DTO
"""

from pydantic import BaseModel, Field, validator
from typing import Optional


class ImageOCRRequestDTO(BaseModel):
    """图片 OCR 请求数据传输对象"""
    
    base64_image: str = Field(
        ...,
        description="Base64 编码的图片数据",
        min_length=1
    )
    
    target_language: str = Field(
        ...,
        description="目标语言代码",
        example="en"
    )
    
    @validator('base64_image')
    def validate_base64_image(cls, v):
        """验证 Base64 图片数据"""
        if not v or len(v.strip()) == 0:
            raise ValueError("Base64 图片数据不能为空")
        
        # 移除可能的 data:image 前缀进行验证
        clean_base64 = v
        if v.startswith('data:image'):
            clean_base64 = v.split(',')[1] if ',' in v else v
        
        # 简单验证 Base64 格式
        import base64
        try:
            base64.b64decode(clean_base64, validate=True)
        except Exception:
            raise ValueError("无效的 Base64 图片数据")
        
        return v
    
    @validator('target_language')
    def validate_target_language(cls, v):
        """验证目标语言"""
        # 支持的语言列表
        supported_languages = [
            'en', 'zh', 'may', 'hkm', 'id', 'bur', 'fil', 'th', 'vie', 'tam', 'lao'
        ]
        
        if v not in supported_languages:
            raise ValueError(f"不支持的目标语言: {v}，支持的语言: {supported_languages}")
        
        return v
    
    @property
    def image_size_estimate(self) -> int:
        """估算图片大小（字节）"""
        # Base64 编码后的大小约为原始大小的 4/3
        clean_base64 = self.base64_image
        if self.base64_image.startswith('data:image'):
            clean_base64 = self.base64_image.split(',')[1] if ',' in self.base64_image else self.base64_image
        
        return len(clean_base64) * 3 // 4
    
    class Config:
        json_schema_extra = {
            "example": {
                "base64_image": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
                "target_language": "en"
            }
        }
