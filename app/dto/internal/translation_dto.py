"""
翻译相关内部传输对象
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from app.models.enums.translation_enums import ProcessingMode, CacheStrategy


@dataclass
class TranslationRequestInfo:
    """翻译请求信息"""
    path: str
    html_length: int
    source_language: str
    target_language: str
    processing_mode: ProcessingMode
    untranslatable_tags: Optional[str]
    no_translate_tags: Optional[str]
    cache_strategy: CacheStrategy


@dataclass
class DomExtractionResults:
    """DOM提取结果"""
    total_text_nodes: int
    total_chinese_segments: int
    unique_chinese_texts: int


@dataclass
class TranslationResults:
    """翻译结果"""
    translations: List[Dict[str, Any]]
    success_count: int
    failed_count: int
    total_count: int
    unique_count: int
    duration: float


@dataclass
class ReplacementStatistics:
    """替换统计"""
    extraction_rate: float
    replacement_rate: float
    damage_rate: float
    original_chinese_count: int
    replaced_count: int
    final_chinese_count: int
    method: str = "unknown"


@dataclass
class UltimateReplacementResults:
    """终极替换结果"""
    original_html_body: str
    translated_html_body: str
    replacement_statistics: ReplacementStatistics
    translation_map: Dict[str, str]


@dataclass
class LargeHtmlProcessingStats:
    """大型HTML处理统计"""
    processing_mode: str
    processing_time: float
    translation_duration: float
    chunks_processed: int
    total_texts: int
    unique_texts: int
    translation_success: int
    translation_failed: int
    replacement_rate: float
    memory_peak_mb: float
    remaining_texts: List[str]


@dataclass
class TranslationResponseData:
    """翻译响应数据"""
    request_info: TranslationRequestInfo
    dom_extraction_results: Optional[DomExtractionResults] = None
    translation_results: Optional[TranslationResults] = None
    ultimate_replacement_results: Optional[UltimateReplacementResults] = None
    ultra_safe_replacement_results: Optional[UltimateReplacementResults] = None
    large_html_results: Optional[Dict[str, Any]] = None
