"""
翻译相关枚举类
"""

from enum import Enum


class LanguageCode(Enum):
    """支持的语言代码"""
    CHINESE = "zh"
    ENGLISH = "en"
    MALAY = "may"
    KHMER = "hkm"
    INDONESIAN = "id"
    BURMESE = "bur"
    FILIPINO = "fil"
    THAI = "th"
    VIETNAMESE = "vie"
    TAMIL = "tam"
    LAO = "lao"


class ProcessingMode(Enum):
    """处理模式"""
    STANDARD = "standard"
    LARGE_HTML = "large_html"
    ULTRA_SAFE = "ultra_safe"


class CacheStrategy(Enum):
    """缓存策略"""
    FILE_CACHE = "file_cache"
    REDIS_CACHE = "redis_cache"


class TranslationStatus(Enum):
    """翻译状态"""
    SUCCESS = "success"
    FAILED = "failed"
    PENDING = "pending"
    CACHED = "cached"


class HtmlSizeCategory(Enum):
    """HTML大小分类"""
    SMALL = "small"      # < 10KB
    MEDIUM = "medium"    # 10KB - 100KB
    LARGE = "large"      # > 100KB
    
    @classmethod
    def categorize(cls, html_length: int) -> 'HtmlSizeCategory':
        """根据HTML长度分类"""
        if html_length < 10000:
            return cls.SMALL
        elif html_length < 100000:
            return cls.MEDIUM
        else:
            return cls.LARGE
