"""
翻译引擎相关实体类
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from datetime import datetime


@dataclass
class TranslationEngineConfig:
    """翻译引擎配置实体"""
    app_id: str
    secret_key: str
    api_url: str
    timeout: float
    max_concurrent: int = 30  # 从配置读取，默认30
    
    def __post_init__(self):
        """验证配置"""
        if not self.app_id or not self.secret_key:
            raise ValueError("翻译引擎配置不完整")
    
    @property
    def secret_key_preview(self) -> str:
        """安全的密钥预览"""
        if not self.secret_key:
            return ""
        return self.secret_key[:8] + "..."


@dataclass
class TranslationRequest:
    """翻译请求实体"""
    text: str
    from_lang: str
    to_lang: str
    request_id: Optional[str] = None
    
    def __post_init__(self):
        """验证请求"""
        if not self.text.strip():
            raise ValueError("翻译文本不能为空")


@dataclass
class TranslationResponse:
    """翻译响应实体"""
    success: bool
    original: str
    translated: Optional[str] = None
    from_lang: Optional[str] = None
    to_lang: Optional[str] = None
    error: Optional[str] = None
    request_id: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class BatchTranslationResult:
    """批量翻译结果实体"""
    translations: List[TranslationResponse]
    success_count: int
    failed_count: int
    total_count: int
    unique_count: int
    duration: float
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.unique_count == 0:
            return 0.0
        return (self.success_count / self.unique_count) * 100
    
    @property
    def translation_speed(self) -> float:
        """翻译速度（文本/秒）"""
        if self.duration == 0:
            return 0.0
        return self.unique_count / self.duration


@dataclass
class ApiRequestParams:
    """API请求参数实体"""
    query: str
    from_lang: str
    to_lang: str
    app_id: str
    salt: str
    sign: str
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典"""
        return {
            'q': self.query,
            'from': self.from_lang,
            'to': self.to_lang,
            'appid': self.app_id,
            'salt': self.salt,
            'sign': self.sign
        }
