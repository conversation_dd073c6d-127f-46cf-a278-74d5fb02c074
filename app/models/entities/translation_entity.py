"""
翻译业务实体类
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from datetime import datetime
from app.models.enums.translation_enums import LanguageCode, ProcessingMode, CacheStrategy, TranslationStatus


@dataclass
class TranslationTask:
    """翻译任务实体"""
    path: str
    html_body: str
    source_language: LanguageCode
    target_language: LanguageCode
    untranslatable_tags: Optional[str] = None
    no_translate_tags: Optional[str] = None
    cache_strategy: CacheStrategy = CacheStrategy.FILE_CACHE
    
    @property
    def html_length(self) -> int:
        """HTML内容长度"""
        return len(self.html_body)
    
    @property
    def is_large_html(self) -> bool:
        """是否为大型HTML"""
        return self.html_length > 100000


@dataclass
class TranslationResult:
    """翻译结果实体"""
    original_text: str
    translated_text: str
    source_language: LanguageCode
    target_language: LanguageCode
    status: TranslationStatus
    error_message: Optional[str] = None
    
    @property
    def is_success(self) -> bool:
        """是否翻译成功"""
        return self.status == TranslationStatus.SUCCESS


@dataclass
class TranslationStatistics:
    """翻译统计实体"""
    total_texts: int
    unique_texts: int
    success_count: int
    failed_count: int
    processing_time: float
    translation_duration: float
    replacement_rate: float
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.unique_texts == 0:
            return 0.0
        return (self.success_count / self.unique_texts) * 100
    
    @property
    def translation_speed(self) -> float:
        """翻译速度（文本/秒）"""
        if self.translation_duration == 0:
            return 0.0
        return self.unique_texts / self.translation_duration


@dataclass
class DomExtractionResult:
    """DOM提取结果实体"""
    total_text_nodes: int
    total_chinese_segments: int
    unique_chinese_texts: int
    chinese_texts: List[str]
    
    @property
    def extraction_efficiency(self) -> float:
        """提取效率"""
        if self.total_text_nodes == 0:
            return 0.0
        return (self.total_chinese_segments / self.total_text_nodes) * 100


@dataclass
class CacheInfo:
    """缓存信息实体"""
    cache_key: str
    path: str
    source_language: LanguageCode
    target_language: LanguageCode
    created_at: datetime
    expires_at: Optional[datetime] = None
    cache_method: str = "unknown"
    
    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
