"""
缓存相关实体类
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from app.models.enums.translation_enums import LanguageCode, CacheStrategy


@dataclass
class CacheKey:
    """缓存键实体"""
    path: str
    source_language: LanguageCode
    target_language: LanguageCode

    def __post_init__(self):
        """验证缓存键"""
        if not self.path.strip():
            raise ValueError("路径不能为空")

    @property
    def normalized_path(self) -> str:
        """标准化路径"""
        path = self.path.lower().rstrip('/')

        # 移除查询参数和锚点
        if '?' in path:
            path = path.split('?')[0]
        if '#' in path:
            path = path.split('#')[0]

        return path

    @property
    def cache_identifier(self) -> str:
        """缓存标识符"""
        return f"{self.normalized_path}|{self.source_language.value}|{self.target_language.value}"


@dataclass
class CacheMetadata:
    """缓存元数据实体"""
    cache_key: str
    created_at: datetime
    expires_at: Optional[datetime]
    source_language: LanguageCode
    target_language: LanguageCode
    path: str
    cache_method: str
    ttl_seconds: Optional[int] = None

    @property
    def is_expired(self) -> bool:
        """是否已过期"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at

    @property
    def remaining_ttl(self) -> int:
        """剩余TTL（秒）"""
        if self.expires_at is None:
            return -1
        remaining = (self.expires_at - datetime.now()).total_seconds()
        return max(0, int(remaining))


@dataclass
class CacheEntry:
    """缓存条目实体"""
    metadata: CacheMetadata
    content: Dict[str, Any]

    @property
    def is_valid(self) -> bool:
        """是否有效"""
        return not self.metadata.is_expired

    @property
    def size_bytes(self) -> int:
        """估算大小（字节）"""
        import json
        return len(json.dumps(self.content, ensure_ascii=False).encode('utf-8'))


@dataclass
class CacheConfig:
    """缓存配置基类"""
    strategy: CacheStrategy
    ttl_seconds: int
    max_size_mb: Optional[int] = None
    compression_enabled: bool = True
    compression_min_size: int = 1024

    def __post_init__(self):
        """验证配置"""
        if self.ttl_seconds <= 0:
            raise ValueError("TTL必须大于0")
        if self.max_size_mb is not None and self.max_size_mb <= 0:
            raise ValueError("最大大小必须大于0")


@dataclass
class FileCacheConfig:
    """文件缓存配置实体"""
    strategy: CacheStrategy
    ttl_seconds: int
    cache_dir: str
    index_file: str
    max_size_mb: Optional[int] = None
    compression_enabled: bool = True
    compression_min_size: int = 1024
    cleanup_interval_hours: int = 24

    def __post_init__(self):
        """验证配置"""
        if self.ttl_seconds <= 0:
            raise ValueError("TTL必须大于0")
        if self.max_size_mb is not None and self.max_size_mb <= 0:
            raise ValueError("最大大小必须大于0")
        if not self.cache_dir.strip():
            raise ValueError("缓存目录不能为空")


@dataclass
class RedisCacheConfig:
    """Redis缓存配置实体"""
    strategy: CacheStrategy
    ttl_seconds: int
    host: str
    port: int
    db: int
    password: Optional[str] = None
    max_size_mb: Optional[int] = None
    compression_enabled: bool = True
    compression_min_size: int = 1024

    def __post_init__(self):
        """验证配置"""
        if self.ttl_seconds <= 0:
            raise ValueError("TTL必须大于0")
        if self.max_size_mb is not None and self.max_size_mb <= 0:
            raise ValueError("最大大小必须大于0")
        if not self.host.strip():
            raise ValueError("Redis主机不能为空")
        if not (1 <= self.port <= 65535):
            raise ValueError("Redis端口必须在1-65535之间")
        if self.db < 0:
            raise ValueError("Redis数据库编号不能为负数")


@dataclass
class CacheStatistics:
    """缓存统计实体"""
    total_entries: int
    total_size_mb: float
    hit_count: int = 0
    miss_count: int = 0
    expired_count: int = 0

    @property
    def hit_rate(self) -> float:
        """命中率"""
        total_requests = self.hit_count + self.miss_count
        if total_requests == 0:
            return 0.0
        return (self.hit_count / total_requests) * 100

    @property
    def miss_rate(self) -> float:
        """未命中率"""
        return 100.0 - self.hit_rate


@dataclass
class LanguagePairStats:
    """语言对统计实体"""
    source_language: LanguageCode
    target_language: LanguageCode
    entry_count: int
    total_size_mb: float

    @property
    def language_pair_name(self) -> str:
        """语言对名称"""
        return f"{self.source_language.value}-{self.target_language.value}"


@dataclass
class CacheOperationResult:
    """缓存操作结果实体"""
    success: bool
    message: str
    data: Optional[Any] = None
    error: Optional[str] = None

    @classmethod
    def success_result(cls, message: str, data: Any = None) -> 'CacheOperationResult':
        """创建成功结果"""
        return cls(success=True, message=message, data=data)

    @classmethod
    def error_result(cls, message: str, error: str = None) -> 'CacheOperationResult':
        """创建错误结果"""
        return cls(success=False, message=message, error=error)
