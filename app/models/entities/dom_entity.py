"""
DOM处理相关实体类
"""

from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup, NavigableString, Tag


@dataclass
class TextNodeInfo:
    """文本节点信息实体"""
    content: str
    chinese_texts: List[str]
    path: str
    element: Any  # BeautifulSoup元素
    parent: Optional[Any] = None
    attr_name: Optional[str] = None  # 如果是属性中的文本
    
    @property
    def is_attribute_text(self) -> bool:
        """是否为属性中的文本"""
        return self.attr_name is not None
    
    @property
    def chinese_count(self) -> int:
        """中文文本数量"""
        return len(self.chinese_texts)


@dataclass
class DomExtractionData:
    """DOM提取数据实体"""
    soup: BeautifulSoup
    text_nodes: List[TextNodeInfo]
    chinese_texts: List[str]
    statistics: Dict[str, int]
    
    @property
    def total_text_nodes(self) -> int:
        """总文本节点数"""
        return len(self.text_nodes)
    
    @property
    def total_chinese_segments(self) -> int:
        """总中文片段数"""
        return len(self.chinese_texts)
    
    @property
    def unique_chinese_texts(self) -> int:
        """唯一中文文本数"""
        return len(set(self.chinese_texts))


@dataclass
class ReplacementResult:
    """替换结果实体"""
    original_html: str
    translated_html: str
    replacement_count: int
    original_chinese_count: int
    remaining_chinese_count: int
    
    @property
    def replacement_rate(self) -> float:
        """替换率"""
        if self.original_chinese_count == 0:
            return 100.0
        replaced_count = self.original_chinese_count - self.remaining_chinese_count
        return (replaced_count / self.original_chinese_count) * 100
    
    @property
    def success_rate(self) -> float:
        """成功率（别名）"""
        return self.replacement_rate


@dataclass
class CssSelector:
    """CSS选择器实体"""
    selector: str
    is_valid: bool = True
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """验证选择器"""
        if not self.selector or not self.selector.strip():
            self.is_valid = False
            self.error_message = "选择器不能为空"


@dataclass
class DomProcessingConfig:
    """DOM处理配置实体"""
    untranslatable_tags: Optional[str] = None
    process_attributes: bool = True
    process_comments: bool = False
    process_scripts: bool = False
    process_styles: bool = True
    chinese_pattern: str = r'[\u4e00-\u9fff]+'
    
    @property
    def allowed_selectors(self) -> List[str]:
        """允许的选择器列表"""
        if not self.untranslatable_tags:
            return []
        return [selector.strip() for selector in self.untranslatable_tags.split(',')]
    
    @property
    def has_tag_filter(self) -> bool:
        """是否有标签过滤"""
        return bool(self.untranslatable_tags)


@dataclass
class ReplacementStatistics:
    """替换统计实体"""
    dom_replacements: int = 0
    special_replacements: int = 0
    brute_force_replacements: int = 0
    total_replacements: int = 0
    processing_time: float = 0.0
    
    def add_dom_replacement(self, count: int):
        """添加DOM替换数量"""
        self.dom_replacements += count
        self._update_total()
    
    def add_special_replacement(self, count: int):
        """添加特殊情况替换数量"""
        self.special_replacements += count
        self._update_total()
    
    def add_brute_force_replacement(self, count: int):
        """添加暴力替换数量"""
        self.brute_force_replacements += count
        self._update_total()
    
    def _update_total(self):
        """更新总替换数量"""
        self.total_replacements = (
            self.dom_replacements + 
            self.special_replacements + 
            self.brute_force_replacements
        )
