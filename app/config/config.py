"""
应用配置管理 - 自动同步.env文件
修改.env文件后自动生效，无需修改此配置文件
"""

from functools import lru_cache
from typing import Any, Optional
from pydantic_settings import BaseSettings



class Settings(BaseSettings):
    """应用配置类 - 自动同步.env文件的所有配置"""

    # ===== 应用基本信息 =====
    app_name: str = "翻译API服务"
    version: str = "1.0.0"

    # ===== 服务器配置 =====
    host: str = "localhost"
    port: int = 9000
    debug: bool = False
    log_level: str = "INFO"
    log_file: Optional[str] = None

    # ===== Redis配置 =====
    redis_host: str = "127.0.0.1"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    redis_max_connections: int = 50
    redis_socket_timeout: float = 5.0
    redis_connect_timeout: float = 5.0
    redis_use_compression: bool = True
    redis_compression_min_size: int = 1024
    redis_compression_level: int = 6

    # ===== 缓存配置 =====
    cache_ttl: int = 86400
    memory_cache_size: int = 1000
    memory_cache_ttl: int = 300

    # ===== 文件缓存配置 =====
    file_cache_ttl_days: int = 7
    file_cache_max_size_mb: int = 1024
    file_cache_cleanup_interval_hours: int = 24

    # ===== 百度API配置 =====
    baidu_app_id: str = ""
    baidu_secret_key: str = ""
    baidu_api_timeout: float = 2.0
    baidu_max_concurrent: int = 30  


    # ===== 请求处理配置 =====
    merge_window: float = 0.1
    max_batch_size: int = 100
    max_concurrent_requests: int = 50



    # ===== JWT认证配置 =====
    jwt_secret: str = "default-jwt-secret"
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 30
    jwt_refresh_expire_days: int = 7

    # ===== DeepSeek翻译配置 =====
    deepseek_api_key: str = ""
    deepseek_api_url: str = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    deepseek_model: str = "ep-20250225134440-fbrnj"
    deepseek_timeout: float = 60.0
    deepseek_max_tokens: int = 4000
    deepseek_temperature: float = 0.3

    # ===== DeepSeek视觉模型配置 =====
    deepseek_vision_model: str = "ep-20241217182352-9b5pf"

    # ===== Qwen3-MT翻译配置 =====
    qwen_mt_api_key: str = ""
    qwen_mt_api_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    qwen_mt_model: str = "qwen-mt-turbo"
    qwen_mt_timeout: float = 60.0
    qwen_mt_max_tokens: int = 2048
    qwen_mt_temperature: float = 0.0

    # ===== 缓存清理配置 =====
    translation_cache_cleanup_interval_minutes: int = 1
    image_cache_cleanup_interval_minutes: int = 5

    # ===== 图片批量处理配置 =====
    image_batch_max_concurrent: int = 5



    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        # 自动从环境变量读取，不区分大小写
        case_sensitive = False
        # 允许额外的字段
        extra = "allow"

    @property
    def redis_url(self) -> str:
        """构建Redis连接URL"""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"



    def get_config_dict(self) -> dict:
        """获取所有配置的字典形式"""
        return self.model_dump()

    def get_env_config(self, key: str, default: Any = None) -> Any:
        """动态获取环境变量配置"""
        return getattr(self, key.lower(), default)



# 全局实例
@lru_cache()
def get_settings() -> Settings:
    """获取应用配置单例"""
    return Settings()



