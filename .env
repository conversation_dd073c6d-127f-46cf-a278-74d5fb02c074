
# ===== 服务器配置 =====
HOST=0.0.0.0
PORT=9000
DEBUG=false
LOG_LEVEL=INFO

# ===== Redis配置 ===== 该配置可关可开 在测试阶段可以关闭基于文件缓存即可 注释掉配置ip即关
# 远程Redis（如果可用）
REDIS_HOST=**************
# 本地Redis（推荐用于开发）
# REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
# REDIS_PASSWORD=
REDIS_PASSWORD=123456
#最大并大量
REDIS_MAX_CONNECTIONS=50
#两个都是超时时间
REDIS_SOCKET_TIMEOUT=5.0
REDIS_CONNECT_TIMEOUT=5.0

# ===== redis缓存配置 =====
#缓存时间 /秒
CACHE_TTL=86400

# ===== 缓存清理配置 =====
# 翻译缓存清理间隔（分钟）
TRANSLATION_CACHE_CLEANUP_INTERVAL_MINUTES=1
# 图片缓存清理间隔（分钟）
IMAGE_CACHE_CLEANUP_INTERVAL_MINUTES=5

# Redis缓存清理配置
REDIS_CACHE_CLEANUP_ENABLED=true
REDIS_CACHE_MAX_KEYS=10000
REDIS_CACHE_CLEANUP_BATCH_SIZE=100

# 文件缓存配置 /作废 请勿修改 
FILE_CACHE_TTL_DAYS=7
FILE_CACHE_MAX_SIZE_MB=1024
FILE_CACHE_CLEANUP_INTERVAL_HOURS=24

# 内存缓存清理配置 /作废 请勿修改 
MEMORY_CACHE_CLEANUP_ENABLED=true
MEMORY_CACHE_MAX_SIZE=1000
MEMORY_CACHE_TTL_MINUTES=60

# 翻译结果缓存清理配置 /作废 请勿修改 
TRANSLATION_CACHE_CLEANUP_ENABLED=true
TRANSLATION_CACHE_MAX_AGE_HOURS=168  # 7天

# ===== 翻译API配置 =====
BAIDU_APP_ID=20250527002366778
BAIDU_SECRET_KEY=9fofmzHg4FvH1B25MfR5
BAIDU_API_TIMEOUT=2.0

# ===== 翻译处理配置 =====
# 并发数
BAIDU_MAX_CONCURRENT=30

# ===== 请求处理配置 =====
MERGE_WINDOW=0.1
#并发数
MAX_BATCH_SIZE=100
MAX_CONCURRENT_REQUESTS=50



# ===== JWT认证配置 =====
#密钥
JWT_SECRET=aM4xZ0yT4jU1fK4q
#算法
JWT_ALGORITHM=HS256
#时间/分
JWT_EXPIRE_MINUTES=30
#过期/天
JWT_REFRESH_EXPIRE_DAYS=7


# ===== DeepSeek翻译配置 =====
DEEPSEEK_API_KEY=bec69062-1d05-4912-8c52-f428c189a8aa
DEEPSEEK_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
DEEPSEEK_MODEL=ep-20250225134440-fbrnj
DEEPSEEK_TIMEOUT=15.0
DEEPSEEK_MAX_TOKENS=4000
DEEPSEEK_TEMPERATURE=0.3

# ===== DeepSeek视觉模型配置 ===== 简称ocr视觉模型
DEEPSEEK_VISION_MODEL=ep-20241217182352-9b5pf

# ===== Qwen3-MT翻译配置 =====
QWEN_MT_API_KEY=sk-c7ed3aca66b64d15b5839c7da3487562
QWEN_MT_API_URL=https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
QWEN_MT_MODEL=qwen-mt-turbo
QWEN_MT_TIMEOUT=60.0
QWEN_MT_MAX_TOKENS=2048
QWEN_MT_TEMPERATURE=0.0

# ===== 图片批量处理配置 =====
# 并发数
IMAGE_BATCH_MAX_CONCURRENT=5

